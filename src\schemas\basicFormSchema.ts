import * as Yup from "yup";

export const getBasicFormSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    jobTitle: Yup.mixed<string | number>().required("Please select job title"),
    otherJob: Yup.string().when("jobTitle", {
      is: "other",
      then: (schema) => schema.required("Please enter other job title"),
      otherwise: (schema) => schema.notRequired(),
    }),
    employmentTypes: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one employment type")
      .required("Please select at least one employment type"),
    numberOfOpenings: Yup.number()
      .transform((_, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .required("Please enter number of openings")
      .min(1, "Number of openings must be atleast 1"),
    locationCity: Yup.string().required("Please enter city"),
    stateId: Yup.string().required("Please select state"),
    routeType: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one route type")
      .required("Please select at least one route type"),
    specificRoute: Yup.string().when("routeType", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter other route"),
      otherwise: (schema) => schema.notRequired(),
    }),
    serviceArea: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one operating area")
      .required("Please select at least one operating area"),
    specificState: Yup.string().when("serviceArea", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter other area"),
      otherwise: (schema) => schema.notRequired(),
    }),
    homeTimeFrequency: Yup.mixed<string | number>().required(
      "Please select home time frequency"
    ),
    customHomeTimeFrequency: Yup.string().when("homeTimeFrequency", {
      is: "other",
      then: (schema) => schema.required("Please enter custom home time"),
      otherwise: (schema) => schema.notRequired(),
    }),
    workScheduleDays: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one work schedule")
      .required("Please select at least one work schedule"),
    joinPeriod: Yup.mixed<string | number>().required(
      "Please select joining period"
    ),
    targetStartDate: Yup.string().when("joinPeriod", {
      is: 76,
      then: (schema) => schema.required("Please select joining date"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
};

export const getBasicSchoolSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    jobTitle: Yup.mixed<string | number>().required("Please select job title"),
    otherJob: Yup.string().when("jobTitle", {
      is: "other",
      then: (schema) => schema.required("Please enter other job title"),
      otherwise: (schema) => schema.notRequired(),
    }),
    employmentTypes: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one employment type")
      .required("Please select at least one employment type"),
    numberOfOpenings: Yup.number()
      .transform((_, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .required("Please enter number of openings")
      .min(1, "Number of openings must be atleast 1"),
    locationCity: Yup.string().required("Please enter city"),
    stateId: Yup.string().required("Please select state"),
    routeType: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one route type")
      .required("Please select at least one route type"),
    specificRoute: Yup.string().when("routeType", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter other route"),
      otherwise: (schema) => schema.notRequired(),
    }),
    workScheduleDays: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one work schedule")
      .required("Please select at least one work schedule"),
    workScheduleTime: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one work timing")
      .required("Please select at least one work timing"),
    joinPeriod: Yup.mixed<string | number>().required(
      "Please select joining period"
    ),
    targetStartDate: Yup.string().when("joinPeriod", {
      is: 76,
      then: (schema) => schema.required("Please select joining date"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
};
