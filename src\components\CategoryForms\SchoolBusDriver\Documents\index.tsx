"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  submitDriverDetails,
  fetchDriverDetails
} from "@/services/driverFormService";
import BrowseFiles, { FileItem } from "@/components/Browse/BrowseFiles";
import css from './schoolBusDriverDoc.module.scss';


interface DocumentFormValues {
  dotMedicalCardFiles: FileItem[];
  twicCardFiles: FileItem[];
  hazmatAndOthersFiles: FileItem[];
}

const Documents: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useSchoolBusDriverCategory();
  const router = useRouter();
  const [dotMedicalCardFiles, setDotMedicalCardFiles] = useState<FileItem[]>([]);
  const [twicCardFiles, setTwicCardFiles] = useState<FileItem[]>([]);
  const [hazmatAndOthersFiles, setHazmatAndOthersFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    const loadDriverDetails = async () => {
      try {
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;


          if (driver.documents) {
            setDotMedicalCardFiles(driver.documents.dot_medical_card || []);
            setTwicCardFiles(driver.documents.twic_card || []);
            setHazmatAndOthersFiles(driver.documents.hazmat_and_others || []);
          }
        }
      } catch (error) {
        console.error("Error fetching driver details:", error);
        toast.error("Failed to load existing documents");
      } finally {
        setIsLoading(false);
      }
    };

    loadDriverDetails();
  }, []);

  const validationSchema = Yup.object({
    dotMedicalCardFiles: Yup.array()
      .min(1, "DOT Medical Card document is required")
      .required("DOT Medical Card document is required"),
    twicCardFiles: Yup.array()
      .min(1, "TWIC Card document is required")
      .required("TWIC Card document is required"),
    hazmatAndOthersFiles: Yup.array()
      .min(1, "Hazmat endorsement proof is required")
      .required("Hazmat endorsement proof is required"),
  });

  const handleSubmit = async (shouldContinue: boolean = true) => {
    try {
      const payload = {
        currentStage: 3,
        currentStep: 4,
        driver: {
          documents: {
            dot_medical_card: dotMedicalCardFiles,
            twic_card: twicCardFiles,
            hazmat_and_others: hazmatAndOthersFiles,
          },
        },
      };
      console.log("Submitting documents payload:", JSON.stringify(payload, null, 2));
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Documents uploaded successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to upload documents. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error("Error submitting documents:", error);
      const errorMessage = error instanceof Error ? error.message : "An error occurred while processing documents.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<DocumentFormValues>({
    initialValues: {
      dotMedicalCardFiles: [],
      twicCardFiles: [],
      hazmatAndOthersFiles: [],
    },
    validationSchema,
    onSubmit: async () => {
      try {
        await handleSubmit(true);
      } catch (error) {
        console.error("Error submitting documents:", error);
        toast.error("An error occurred while uploading documents.");
      }
    },
  });

  useEffect(() => {
    formik.setFieldValue("dotMedicalCardFiles", dotMedicalCardFiles);
  }, [dotMedicalCardFiles]);

  useEffect(() => {
    formik.setFieldValue("twicCardFiles", twicCardFiles);
  }, [twicCardFiles]);

  useEffect(() => {
    formik.setFieldValue("hazmatAndOthersFiles", hazmatAndOthersFiles);
  }, [hazmatAndOthersFiles]);

  if (isLoading) {
    return <div>Loading existing documents...</div>;
  }



  return (
    <div className={css.driversRegistration}>
      <div style={{ marginBottom: "2rem" }}>
        <h2>Step 4: Additional Document Uploads (School Bus Driver)</h2>
        <h5>Upload required and relevant documents. Your CDL/ID was uploaded in Stage 2. These docs support your qualifications and will only be shared via approved &quot;Hiring Packet Requests&quot;.</h5>
        <h5>(Accepted formats: JPG, PNG, PDF. Max size: 5MB per file)</h5>
        <h6 className={css.required}>Required fields are marked with <span>*</span> and depend on your selections in Step 3</h6>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className={css.flexBox}>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label htmlFor="">Upload DOT Medical Card and Medical Variance/Exemption Document&nbsp;<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setDotMedicalCardFiles}
              initialFiles={dotMedicalCardFiles}
            />
            {formik.touched.dotMedicalCardFiles && formik.errors.dotMedicalCardFiles && (
              <span className={css.error}>
                {typeof formik.errors.dotMedicalCardFiles === 'string'
                  ? formik.errors.dotMedicalCardFiles
                  : 'DOT Medical Card document is required'}
              </span>
            )}
          </div>

          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label htmlFor="">Upload Scan / Photo of TWIC Card&nbsp;<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setTwicCardFiles}
              initialFiles={twicCardFiles}
            />
            {formik.touched.twicCardFiles && formik.errors.twicCardFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.twicCardFiles === 'string'
                  ? formik.errors.twicCardFiles
                  : 'TWIC Card document is required'}
              </p>
            )}
          </div>

          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label htmlFor="">Upload Hazmat Endorsement Proof and Any Other Relevant Certifications&nbsp;<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setHazmatAndOthersFiles}
              initialFiles={hazmatAndOthersFiles}
            />
            {formik.touched.hazmatAndOthersFiles && formik.errors.hazmatAndOthersFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.hazmatAndOthersFiles === 'string'
                  ? formik.errors.hazmatAndOthersFiles
                  : 'Hazmat endorsement proof is required'}
              </p>
            )}
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              className={css.back}
            >
              <img src="/images/icons/arrow_back.svg"/>
              Back
            </button>
          )}
          <button
            type="button"
            onClick={() => handleSubmit(false)}
            disabled={isLoading}
            className={css.exit}
          >
            Save & Exit
          </button>
          <button
              type="submit"
              disabled={isLoading}
              className={css.continue}
            >
              {isLoading ? "Saving..." : "Save & Continue (To Step 5)"}
            </button>
        </div>
      </form>
    </div>
  );
};

export default Documents;
