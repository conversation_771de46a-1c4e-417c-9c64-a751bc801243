"use client";
import React, { useEffect, useState } from "react";
import { useFormik, FormikErrors } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchSchoolBusDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import css from './workHistory.module.scss';

interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface WorkPeriod {
  id: string;
  periodType: string;
  employerName: string;
 
  address: Address;
  phone: string;
  employerManagerName: string;
  employerWebsite: string;
  position: string;
  subjectToFmcsa: string;
  operatedCmv: string;
  mayContact: string;
  startDate: Date | null;
  endDate: Date | null;
  currentlyWorking: boolean;
  reasonForLeaving: string;
  explanation: string;
}

interface WorkHistoryFormValues {
  workHistory: WorkPeriod[];
}

// Use the exact type from the API response
type EmploymentHistoryApiData = FetchDriverDetailsResponse["data"]["driver"]["driverEmploymentHistory"][0];

function isWorkPeriodErrorArray(
  value: unknown
): value is Array<FormikErrors<WorkPeriod>> {
  return Array.isArray(value);
}

const WorkHistory: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useSchoolBusDriverCategory();
  const router = useRouter();
  const [periodTypeOptions, setPeriodTypeOptions] = useState<FormValue[]>([]);
  const [reasonOptions, setReasonOptions] = useState<FormValue[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  console.log("**periodTypeOptions",periodTypeOptions,"reasonOptions",reasonOptions)

  // Helper function to check if a period type is unemployment
  const isUnemploymentPeriod = (periodTypeId: string): boolean => {
    if (!periodTypeId || !periodTypeOptions.length) return false;
    const option = periodTypeOptions.find(opt => opt.formValueId.toString() === periodTypeId.toString());
    return option?.label.en.toLowerCase().includes("unemployment") || false;
  };

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [formFields, stateList, detailsResponse] = await Promise.all([
          fetchSchoolBusDriverFormFields(),
          getStates(),
          fetchDriverDetails(),
          
        ]);

        // Map API data to state and filter out empty options
        const periodTypes = (formFields["type-of-period-driver-school-bus-driver"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");
        const reasonsForLeaving = (formFields["reason-for-leaving-ending-period-driver-school-bus-driver"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");

        setPeriodTypeOptions(periodTypes);
        setReasonOptions(reasonsForLeaving);
        setStates(stateList);

        // Pre-populate with existing data if available
        const history = detailsResponse?.data?.driver?.driverEmploymentHistory;
          console.log("✅ API se aaya driverEmploymentHistory:", history);
        if (history && history.length > 0) {
          
          const mappedHistory = history.map((emp: EmploymentHistoryApiData) => ({
            
            id: `work-${emp.driverEmploymentHistoryId || Date.now()}-${Math.random()}`,
           periodType: emp.typeOfEmployment?.toString() || "", 
             
            employerName: emp.employerName || "",
            phone: emp.employerPhone || "",
            employerManagerName: emp.employerManagerName || "",
            employerWebsite: emp.employerWebsite || "",
            address: {
              street: emp.employerStreet || "",
              city: emp.employerCity || "",
              state: emp.employerState?.toString() || "",
              zipCode: emp.employerZip || "",
            },
            position: emp.positionHeld || "",
            subjectToFmcsa: emp.subjectToFmcsa ? "yes" : "no",
            operatedCmv: emp.operatedCmv ? "yes" : "no",
            mayContact: emp.contactPermission ? "yes" : "no",
            startDate: emp.startDate ? new Date(emp.startDate) : null,
            endDate: emp.endDate ? new Date(emp.endDate) : null,
            currentlyWorking: emp.isCurrent || false,
            reasonForLeaving: emp.reasonForLeaving?.toString() || "",
            explanation: emp.explanation || "",
         
          }));
console.log("🔍 mappedHistory with typeOfEmployment:", mappedHistory)
          formik.setFieldValue("workHistory", mappedHistory);
        }
      } catch (err) {
        console.error("Failed to fetch data:", err);
        toast.error("Failed to load form data. Please refresh the page.");
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, []);

  const createEmptyWorkPeriod = (): WorkPeriod => ({
    id: `work-${Date.now()}-${Math.random()}`,
    periodType: "",
    employerName: "",
     
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    phone: "",
    employerManagerName: "",
    employerWebsite: "",
    position: "",
    subjectToFmcsa: "yes",
    operatedCmv: "yes",
    mayContact: "yes",
    startDate: null,
    endDate: null,
    currentlyWorking: false,
    reasonForLeaving: "",
    explanation: "",
  });

  const initialValues: WorkHistoryFormValues = {
    workHistory: [createEmptyWorkPeriod()],
  };

  const createValidationSchema = () => Yup.object().shape({
    workHistory: Yup.array()
      .min(3, "At least 3 work/unemployment period is required")
      .of(
        Yup.object().shape({
          periodType: Yup.string().required("Period type is required"),
          employerName: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.required("District/Company name is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          phone: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) =>
              schema
                .matches(/^[\d\s\-\(\)\+\.]{10,}$/, "Please enter a valid phone number")
                .required("Phone number is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          address: Yup.object().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.shape({
              street: Yup.string().required("Street address is required"),
              city: Yup.string().required("City is required"),
              state: Yup.string().required("State is required"),
              zipCode: Yup.string()
                .matches(/^$|^\d{5}(-?\d{4})?$/, "Enter a valid zip code")
                .required("ZipCode is required"),
            }),
            otherwise: (schema) => schema.shape({
              street: Yup.string().notRequired(),
              city: Yup.string().notRequired(),
              state: Yup.string().notRequired(),
              zipCode: Yup.string().notRequired(),
            }),
          }),
          position: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.required("Position is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          subjectToFmcsa: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.required("Please select if you were subject to FMCSA testing"),
            otherwise: (schema) => schema.notRequired(),
          }),
          operatedCmv: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.required("Please select if you operated a CMV"),
            otherwise: (schema) => schema.notRequired(),
          }),
          mayContact: Yup.string().when("periodType", {
            is: (val: string) => {
              if (!val) return false;
              return !isUnemploymentPeriod(val);
            },
            then: (schema) => schema.required("Please select if we may contact this employer"),
            otherwise: (schema) => schema.notRequired(),
          }),
          startDate: Yup.date().nullable().required("Start date is required"),
          endDate: Yup.date()
            .nullable()
            .when("currentlyWorking", {
              is: false,
              then: (schema) => schema.required("End date is required"),
              otherwise: (schema) => schema.notRequired(),
            }),
          reasonForLeaving: Yup.string().required("Reason for leaving is required"),
          // explanation: Yup.string().max(250, "Explanation must be 250 characters or less"),
explanation: Yup.string().when("reasonForLeaving", {
  is: (val: string) =>
    val === "Unemployment Period" ||
    // val === "Other (Specify Below)" ||
    val?.startsWith("Terminated"),
  then: (schema) =>
    schema.required("Explanation required for this reason.").max(250, "Explanation must be 250 characters or less"),
  otherwise: (schema) => schema.max(250, "Explanation must be 250 characters or less"),
}),


        })
      ),
  });

  const handleSubmit = async (values: WorkHistoryFormValues, shouldContinue: boolean = true) => {
    console.log("Form submission started with values:", values);

    if (!values.workHistory || values.workHistory.length < 3) {
      toast.error("At least 3 work/unemployment periods are required.");
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        currentStage: 3,
        currentStep: 2,
        driver: {
          driverEmploymentHistory: values.workHistory.map((period, index) => {
            const isUnemployment = isUnemploymentPeriod(period.periodType);
            return {
              typeOfEmployment: period.periodType ? parseInt(period.periodType) : undefined,
              isUnemployment,
              employerName: isUnemployment ? undefined : period.employerName,
              employerStreet: isUnemployment ? undefined : period.address.street,
              employerCity: isUnemployment ? undefined : period.address.city,
              employerState: isUnemployment ? undefined : period.address.state,
              employerManagerName: isUnemployment ? undefined : period.employerManagerName,
              employerZip: isUnemployment ? undefined : period.address.zipCode,
              employerPhone: isUnemployment ? undefined : period.phone,
              employerWebsite: isUnemployment ? undefined : period.employerWebsite,
              positionHeld: isUnemployment ? undefined : period.position,
              subjectToFmcsa: isUnemployment ? undefined : period.subjectToFmcsa === "yes",
              operatedCmv: isUnemployment ? undefined : period.operatedCmv === "yes",
              contactPermission: isUnemployment ? undefined : period.mayContact === "yes",
              startDate: period.startDate ? new Date(period.startDate).toISOString() : null,
              endDate: period.currentlyWorking ? undefined : period.endDate ? new Date(period.endDate).toISOString() : null,
              isCurrent: period.currentlyWorking,
              reasonForLeaving: period.reasonForLeaving || undefined,
              explanation: period.explanation || "",
              rank: index + 1,
            };
          }),
        },
      };

      console.log("Submitting payload:", JSON.stringify(payload, null, 2));
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Work history saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save work history. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting work history:", error);
      toast.error("Failed to save work history. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<WorkHistoryFormValues>({
    initialValues,
    validationSchema: createValidationSchema(),
    onSubmit: async (values) => {
      console.log("Form submission started with values:", values);
      await handleSubmit(values, true);
    },
  });

  const addWorkPeriod = () => {
    const newPeriod = createEmptyWorkPeriod();
    formik.setFieldValue("workHistory", [
      ...formik.values.workHistory,
      newPeriod,
    ]);
  };

  const removeWorkPeriod = (index: number) => {
    const updated = [...formik.values.workHistory];
    updated.splice(index, 1);
    formik.setFieldValue("workHistory", updated);
  };

  if (isLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>Loading work history...</div>
      </div>
    );
  }

  return (
    <div className={css.workHistory}>
        <h3>Step 2: Work History (Last 3 Years Required)</h3>
        <div className={css.note}>
            <h6>Important:</h6>
            <p>Per DOT regulations (49 CFR §391.21), provide a complete history for the <strong>past 3 years</strong>. 
            Include all school bus driving roles, other employment, and any unemployment periods. Providing 10 years is recommended. 
            Verifications are required by law.</p>
        </div>
        <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>
        <label className={css.mb16}>Work / Unemployment Periods (Start with most recent)</label>
        <button
          type="button"
          onClick={addWorkPeriod}
          className={css.addPeriodBtn}
        >
          + Add Work/Unemployment Period
        </button>
  {formik.touched.workHistory &&
          formik.errors.workHistory &&
          typeof formik.errors.workHistory === "string" && (
            <span className={css.error}>
              {formik.errors.workHistory}
            </span>
        )}
      <form onSubmit={formik.handleSubmit} className={css.drivingExperienceForm}>
        {formik.values.workHistory.map((period, index) => {
          const isCurrentPeriodUnemployment = isUnemploymentPeriod(period.periodType);
          
          return (
          <div key={period.id} className={css.card}>
            <h3>Period {index + 1}</h3>
            {formik.values.workHistory.length > 1 && (
              <button
                type="button"
                onClick={() => removeWorkPeriod(index)}
                className={css.removeHistory}
              >
                <img src="/images/icons/icon-close.svg" alt="icon-close.svg"/>
                Remove This Period
              </button>
            )}

            <div className={css.formRow}>
              {/* Period Type */}
              <div className={css.col01}>
                <div className={css.labelDiv}>
                  <label>Type of Period:&nbsp;<sup>*</sup></label>
                </div>
                <Dropdown
                  options={periodTypeOptions.map(option => ({
                    value: option.formValueId.toString(),
                    label: option.label.en
                  }))}
                  value={period.periodType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue(`workHistory[${index}].periodType`, value)}
                  error={formik.touched.workHistory?.[index]?.periodType &&
                    isWorkPeriodErrorArray(formik.errors.workHistory) &&
                    formik.errors.workHistory[index]?.periodType ?
                    formik.errors.workHistory[index]?.periodType : undefined}
                  name={`workHistory[${index}].periodType`}
                />
              </div>

              {/* Employment Fields (shown when not unemployment) */}
              {period.periodType && !isCurrentPeriodUnemployment && (
                <>
                  {/* Employer Name */}
                  <div className={css.col01}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">School District / Company Name:&nbsp;<sup>*</sup></label>
                    </div>
                    <input
                      type="text"
                      name={`workHistory[${index}].employerName`}
                      value={period.employerName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter District or Company Name"
                    />
                    {formik.touched.workHistory?.[index]?.employerName &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.employerName && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.employerName}
                        </span>
                      )}
                  </div>

                  {/* Address Fields */}
                  <div className={css.col01}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">District / Company Full Address:&nbsp;<sup>*</sup></label>
                      </div>
                    <div className={css.flex}>
                      <div className={css.col02}>
                          <input
                              type="text"
                              name={`workHistory[${index}].address.street`}
                              value={period.address.street}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              placeholder="Street Address"
                            />
                      </div>

                      <div className={css.col02}>
                        <input
                          type="text"
                          name={`workHistory[${index}].address.city`}
                          value={period.address.city}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="City"
                        />
                      </div>

                      <div className={css.col02}>
                        <Dropdown
                          options={states.map(state => ({
                            value: state.stateId.toString(),
                            label: state.name.en
                          }))}
                          value={period.address.state}
                          placeholder="Select State"
                          onChange={(value) => formik.setFieldValue(`workHistory[${index}].address.state`, value)}
                          error={formik.touched.workHistory?.[index]?.address?.state &&
                            isWorkPeriodErrorArray(formik.errors.workHistory) &&
                            formik.errors.workHistory[index]?.address?.state ?
                            formik.errors.workHistory[index]?.address?.state : undefined}
                          name={`workHistory[${index}].address.state`}
                        />
                      </div>
                      
                      <div className={css.col02}>
                        <input
                          type="text"
                          name={`workHistory[${index}].address.zipCode`}
                          value={period.address.zipCode}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Zip Code"
                        />  
                      </div>
                      {formik.touched.workHistory?.[index]?.address?.street &&
                        isWorkPeriodErrorArray(formik.errors.workHistory) &&
                        formik.errors.workHistory[index]?.address?.street && (
                          <span className={css.error}>
                            {formik.errors.workHistory[index]?.address?.street}
                          </span>
                        )}
                      {formik.touched.workHistory?.[index]?.address?.city &&
                        isWorkPeriodErrorArray(formik.errors.workHistory) &&
                        formik.errors.workHistory[index]?.address?.city && (
                          <span className={css.error}>
                            {formik.errors.workHistory[index]?.address?.city}
                          </span>
                        )}
                      {formik.touched.workHistory?.[index]?.address?.state &&
                        isWorkPeriodErrorArray(formik.errors.workHistory) &&
                        formik.errors.workHistory[index]?.address?.state && (
                          <span className={css.error}>
                            {formik.errors.workHistory[index]?.address?.state}
                          </span>
                        )}
                      {formik.touched.workHistory?.[index]?.address?.zipCode &&
                        isWorkPeriodErrorArray(formik.errors.workHistory) &&
                        formik.errors.workHistory[index]?.address?.zipCode && (
                          <span className={css.error}>
                            {formik.errors.workHistory[index]?.address?.zipCode}
                          </span>
                        )}
                    </div>
                  </div>

                  {/* Phone Number */}
                  <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Contact Phone Number (Transportation Dept / HR):&nbsp;<sup>*</sup><span>Required for verification purposes)</span></label>
                    </div>
                    <input
                      type="tel"
                      name={`workHistory[${index}].phone`}
                      value={period.phone}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="(*************"
                    />
                    {formik.touched.workHistory?.[index]?.phone &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.phone && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.phone}
                        </span>
                      )}
                  </div>

                  {/* Manager Name */}
                  <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Manager/Supervisor Name:</label>
                    </div>
                    <input
                      type="text"
                      name={`workHistory[${index}].employerManagerName`}
                      value={period.employerManagerName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter manager or supervisor name"
                    />
                    {formik.touched.workHistory?.[index]?.employerManagerName &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.employerManagerName && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.employerManagerName}
                        </span>
                      )}
                  </div>

                  {/* Website */}
                  <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Company Website (Optional):</label>
                    </div>
                    <input
                      type="url"
                      name={`workHistory[${index}].employerWebsite`}
                      value={period.employerWebsite}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="https://www.company.com"
                    />
                    {formik.touched.workHistory?.[index]?.employerWebsite &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.employerWebsite && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.employerWebsite}
                        </span>
                      )}
                  </div>

                  {/* Position */}
                  <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Position Held:&nbsp;<sup>*</sup></label>
                    </div>
                    <input
                      type="text"
                      name={`workHistory[${index}].position`}
                      value={period.position}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="e.g., School Bus Driver, Substitute Driver, Trainer"
                    />
                    {formik.touched.workHistory?.[index]?.position &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.position && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.position}
                        </span>
                      )}
                  </div>

                  {/* FMCSA Testing */}
                  <div className={`${css.formRow} ${css.dBlaco} ${css.mb0}`}>
                    <label className={css.mb16}>
                      Were you subject to FMCSA Drug & Alcohol Testing Regulations in this role?<sup>*</sup><span>(Generally Yes for school bus drivers operating CDL-required vehicles)</span>
                    </label>
                    <ul className={css.radioList}>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].subjectToFmcsa`}
                          value="yes"
                          checked={period.subjectToFmcsa === "yes"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>Yes</p>
                      </li>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].subjectToFmcsa`}
                          value="no"
                          checked={period.subjectToFmcsa === "no"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>No</p>
                      </li>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].subjectToFmcsa`}
                          value="unsure"
                          checked={period.subjectToFmcsa === "unsure"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>Unsure</p>
                      </li>
                    </ul>
                    {formik.touched.workHistory?.[index]?.subjectToFmcsa &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.subjectToFmcsa && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.subjectToFmcsa}
                        </span>
                      )}
                  </div>

                  {/* CMV Operation */}
                  <div className={`${css.formRow} ${css.dBlaco} ${css.mb0}`}>
                    <label className={css.mb16}>
                      Did you operate a Commercial Motor Vehicle (CMV) requiring a CDL in this role?<sup>*</sup><span>(Most full-size school buses require a CDL B with P and S endorsements)</span>
                    </label>
                    <ul className={css.radioList}>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].operatedCmv`}
                          value="yes"
                          checked={period.operatedCmv === "yes"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>Yes</p>
                      </li>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].operatedCmv`}
                          value="no"
                          checked={period.operatedCmv === "no"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>No</p>
                      </li>
                    </ul>
                    {formik.touched.workHistory?.[index]?.operatedCmv &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.operatedCmv && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.operatedCmv}
                        </span>
                      )}
                  </div>

                  {/* Contact Permission */}
                  <div className={`${css.formRow} ${css.dBlaco} ${css.mb0}`}>
                    <label className={css.mb16}>
                      May we contact this District/Company for verification?<sup>*</sup>
                    </label>
                    <ul className={css.radioList}>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].mayContact`}
                          value="yes"
                          checked={period.mayContact === "yes"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                        <span className={css.checkmark}></span>
                        <p>Yes</p>
                      </li>
                      <li className={css.radioGroup}>
                        <input
                          type="radio"
                          name={`workHistory[${index}].mayContact`}
                          value="no"
                          checked={period.mayContact === "no"}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          style={{ marginRight: "0.5rem" }}
                        />
                        <span className={css.checkmark}></span>
                        <p>No</p>
                      </li>
                    </ul>
                    {formik.touched.workHistory?.[index]?.mayContact &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.mayContact && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.mayContact}
                        </span>
                      )}
                  </div>
                </>
              )}

              {/* Date Fields (shown for all period types) */}
              <div className={`${css.formRow} ${css.mb0}`}>
                <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Dates:&nbsp;<span>(From)</span>&nbsp;<sup>*</sup></label>
                    </div>
                    <DateInput
                      selected={period.startDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].startDate`, date)
                      }
                      placeholder="Start Date"
                    />
                    {formik.touched.workHistory?.[index]?.startDate &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                    formik.errors.workHistory[index]?.startDate && (
                      <span className={css.error}>
                        {formik.errors.workHistory[index]?.startDate}
                      </span>
                    )}
                </div>
                <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Dates:&nbsp;<span>(To)</span>&nbsp;<sup>*</sup></label>
                    </div>
                    <DateInput
                      selected={period.endDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].endDate`, date)
                      }
                      placeholder="End Date or Present"
                      disabled={period.currentlyWorking}
                    />
                    {formik.touched.workHistory?.[index]?.endDate &&
                      isWorkPeriodErrorArray(formik.errors.workHistory) &&
                      formik.errors.workHistory[index]?.endDate && (
                        <span className={css.error}>
                          {formik.errors.workHistory[index]?.endDate}
                        </span>
                      )}
                </div>
                <div className={css.col03}>
                    <div className={css.labelDiv}>
                        <label htmlFor="">Reason for Leaving / Ending Period:&nbsp;<sup>*</sup></label>
                    </div>
                    <Dropdown
                      options={reasonOptions.map(option => ({
                        value: option.formValueId.toString(),
                        label: option.label.en
                      }))}
                      value={period.reasonForLeaving}
                      placeholder="Select Reason"
                      onChange={(value) => formik.setFieldValue(`workHistory[${index}].reasonForLeaving`, value)}
                      error={formik.touched.workHistory?.[index]?.reasonForLeaving &&
                        isWorkPeriodErrorArray(formik.errors.workHistory) &&
                        formik.errors.workHistory[index]?.reasonForLeaving ?
                        formik.errors.workHistory[index]?.reasonForLeaving : undefined}
                      name={`workHistory[${index}].reasonForLeaving`}
                    />
                </div>
                <div className={css.col01}>
                  <div className={css.labelDiv}>
                    <label htmlFor="">Brief Explanation (Required only if &apos;Other&apos;, &apos;Terminated&apos;, or &apos;Unemployment&apos; selected):</label>
                  </div>
                  <textarea
                    name={`workHistory[${index}].explanation`}
                    value={period.explanation}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Briefly explain reason if needed... Max 250 characters"
                    maxLength={250}
                    rows={3}
                  />
                  <div className={css.characterLimit}>
                    {period.explanation.length}/250 characters
                  </div>
                  {formik.touched.workHistory?.[index]?.explanation &&
                  isWorkPeriodErrorArray(formik.errors.workHistory) &&
                  formik.errors.workHistory[index]?.explanation && (
                    <span className={css.error}>
                      {formik.errors.workHistory[index]?.explanation}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )})}

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              className={css.back}
            >
              <img src="/images/icons/arrow_back.svg" alt="Back" />Back
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            onClick={async () => {
              console.log("Submit button clicked");
              console.log("Form errors:", JSON.stringify(formik.errors, null, 2));
              console.log("Form touched:", JSON.stringify(formik.touched, null, 2));
              console.log("Form values:", JSON.stringify(formik.values, null, 2));
              console.log("Form isValid:", formik.isValid);
              console.log("Form isSubmitting:", formik.isSubmitting);

              // Manually validate and submit
              const errors = await formik.validateForm();
              console.log("Manual validation errors:", JSON.stringify(errors, null, 2));

              if (Object.keys(errors).length === 0) {
                console.log("No validation errors, submitting...");
                await handleSubmit(formik.values, true);
              } else {
                console.log("Validation failed, not submitting");
                // Show specific validation errors
                if (errors.workHistory && Array.isArray(errors.workHistory)) {
                  errors.workHistory.forEach((periodError, index) => {
                    if (periodError && typeof periodError === 'object') {
                      console.log(`Period ${index + 1} errors:`, periodError);
                    }
                  });
                }
              }
            }}
            className={css.exit}
          >
            {isLoading ? "Saving..." : "Save & Continue"}
          </button>

          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            disabled={isLoading}
            className={css.continue}
          >
            Save & Exit (Complete Later)
          </button>
        </div>
      </form>
    </div>
  );
};

export default WorkHistory;