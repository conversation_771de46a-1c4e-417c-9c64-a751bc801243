import React from "react";
import styles from "./../jobPosting.module.scss";
import Image from "next/image";
import { jobTitle } from "./DynamicComponentMap";

interface Step {
  title: string,
  subCategory: string,
  component: React.ReactNode;
}

interface StepProps {
  currentStep: number;
  steps: Step[];
  jobCategoryId: string | number;
}

const CompanyWithStepDetails: React.FC<StepProps> = ({
  currentStep,
  steps,
  jobCategoryId
}) => {
  return (
    <div className={styles.sectionWrapper}>
      <div className={styles.companyWrapper}>
        <div className={styles.companyDetails}>
          <Image
            src="/images/icons/truck-photo.svg"
            alt="company-name"
            width={0}
            height={0}
            style={{ width: 'auto', height: 36 }}
          />
          <div>
            <h3>RMP Trucking LLC</h3>
            <p>Trucking Company (Carrier/Broker)</p>
          </div>
        </div>
        <div className={styles.locationDetails}>
          <Image
            src="/images/icons/location.svg"
            alt="location"
            width={16}
            height={20}
          />
          <p>Truckville, TX</p>
        </div>
      </div>
      <div className={styles.title}>
        Job Posting: <strong>{jobTitle[jobCategoryId]}</strong>
      </div>
      <span className={styles.description}>
        {steps[currentStep - 1]?.subCategory}
      </span>
      <div className={styles.stepContainer}>
        <div className={styles.tickIconRow}>
          {steps.map((step, index) => (
            <React.Fragment key={step?.title}>
              <div className={styles.stepWrapper}>
                <div className={styles.step}>
                  {index + 1 <= currentStep ? (
                    <Image
                      src="/images/icons/icon-tick-green.svg"
                      alt="verified-icon"
                      width={28}
                      height={28}
                    />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
              </div>

              <div
                className={`${styles.connectLine} ${
                  index + 1 < currentStep && styles.connectActive
                }`}
              />
            </React.Fragment>
          ))}
        </div>

        <div className={styles.labelRow}>
          {steps.map((step, index) => (
            <div
              className={`${styles.label} ${
                index + 1 <= currentStep && styles.labelColor
              }`}
              key={step?.title}
            >
              {step?.title}
            </div>
          ))}
        </div>
      </div>
      <div className={styles.notes}>
        Required fields are marked with <span>*</span>
      </div>
    </div>
  );
};

export default CompanyWithStepDetails;
