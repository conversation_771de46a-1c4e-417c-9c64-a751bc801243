"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse,
} from "@/services/driverFormService";
import BrowseFiles, { FileItem } from "@/components/Browse/BrowseFiles";
import css from './nemtDocuments.module.scss';

interface DocumentsFormValues {
  dotMedicalCardFiles: FileItem[];
  twicCardFiles: FileItem[];
  hazmatAndOthersFiles: FileItem[];
}

interface MedicalRequirements {
  dotRequired: boolean;
  cprRequired: boolean;
  firstAidRequired: boolean;
  patsRequired: boolean;
  mavoRequired: boolean;
}

// Type extension for NEMT driver specific properties
type NemtDriverData = FetchDriverDetailsResponse["data"]["driver"] & {
  dotRequired?: boolean | null;
  mavoCertification?: boolean | null;
};



const NemtDocuments: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [medicalRequirements, setMedicalRequirements] = useState<MedicalRequirements>({
    dotRequired: false,
    cprRequired: false,
    firstAidRequired: false,
    patsRequired: false,
    mavoRequired: false,
  });


  const validationSchema = Yup.object({
    dotMedicalCardFiles: Yup.array().when([], {
      is: () => medicalRequirements.dotRequired,
      then: (schema) => schema.min(1, "DOT Medical Card documents are required"),
      otherwise: (schema) => schema,
    }),
    twicCardFiles: Yup.array().min(1, "TWIC Card documents are required"),
    hazmatAndOthersFiles: Yup.array().min(1, "HAZMAT and other certification documents are required"),
  });

  const formik = useFormik<DocumentsFormValues>({
    initialValues: {
      dotMedicalCardFiles: [],
      twicCardFiles: [],
      hazmatAndOthersFiles: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  // Load medical data and existing documents
  useEffect(() => {
    const loadDriverData = async () => {
      try {
        setIsLoading(true);
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver as NemtDriverData;

          // Determine requirements based on medical responses
          const requirements: MedicalRequirements = {
            dotRequired: driver.dotRequired === true || Boolean(driver.dotMedicalCardStatus),
            cprRequired: driver.cprCertification === true,
            firstAidRequired: driver.firstAidCertification === true,
            patsRequired: driver.patsCertification === true || driver.patsCertification === false,
            mavoRequired: driver.mavoCertification === true || driver.mavoCertification === false,
          };

          console.log('📋 Document requirements based on medical data:', requirements);
          setMedicalRequirements(requirements);

          // Load existing documents if available
          if (driver.documents) {
            const existingDocs = {
              dotMedicalCardFiles: driver.documents.dot_medical_card || [],
              twicCardFiles: driver.documents.twic_card || [],
              hazmatAndOthersFiles: driver.documents.hazmat_and_others || [],
            };

            console.log('📄 Loading existing documents:', {
              dotMedicalCard: existingDocs.dotMedicalCardFiles.length,
              twicCard: existingDocs.twicCardFiles.length,
              hazmatAndOthers: existingDocs.hazmatAndOthersFiles.length,
              totalFiles: existingDocs.dotMedicalCardFiles.length + existingDocs.twicCardFiles.length + existingDocs.hazmatAndOthersFiles.length
            });

            // Set form values with existing documents
            formik.setValues(existingDocs);
          } else {
            console.log('📄 No existing documents found in API response');
          }
        }
      } catch (error) {
        console.error("Error loading driver data:", error);
        toast.error("Failed to load existing data");
      } finally {
        setIsLoading(false);
      }
    };

    loadDriverData();
  }, []);



  const handleSubmit = async (values: DocumentsFormValues, shouldContinue: boolean) => {
    try {
      setIsSubmitting(true);

      // Validate required documents before submission
      const validationErrors: string[] = [];

      if (medicalRequirements.dotRequired && values.dotMedicalCardFiles.length === 0) {
        validationErrors.push("DOT Medical Card documents are required");
      }

      if (values.twicCardFiles.length === 0) {
        validationErrors.push("TWIC Card documents are required");
      }

      if (values.hazmatAndOthersFiles.length === 0) {
        validationErrors.push("HAZMAT and other certification documents are required");
      }

      // Show toast messages for validation errors
      if (validationErrors.length > 0) {
        validationErrors.forEach(error => {
          toast.error(error);
        });
        setIsSubmitting(false);
        return;
      }

      // Helper function to format files for API
      const formatFilesForApi = (files: FileItem[]) => {
        return files.map(file => ({
          filename: file.filename || '',
          filepath: file.filepath || '',
          ...(file.multimediaId && { multimediaId: file.multimediaId })
        }));
      };

      // Prepare document data with file information
      const payload = {
        currentStage: 3,
        currentStep: 4,
        driver: {
          documents: {
            dot_medical_card: formatFilesForApi(values.dotMedicalCardFiles),
            twic_card: formatFilesForApi(values.twicCardFiles),
            hazmat_and_others: formatFilesForApi(values.hazmatAndOthersFiles),
          }
        }
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Documents uploaded successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to upload documents. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting documents:", error);
      toast.error("Failed to upload documents. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };



  if (isLoading) {
    return (
      <div className={css.loading}>
        <p>Loading existing documents and requirements...</p>
      </div>
    );
  }

  return (
    <div className={css.documents}>
      <h3>Step 4: Additional Document Uploads (NEMT Driver)</h3>
      <p>Upload required certifications and other relevant documents. Your basic ID/License was uploaded in Stage 2. Documents are shared only via approved Hiring Packet Requests.</p>
      <p className={css.fileInfo}><em>(Accepted formats: JPG, PNG, PDF. Max size: 5MB per file)</em></p>
      
      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Card */}
        <div className={css.section}>
          <h4>DOT Medical Card {medicalRequirements.dotRequired && <span style={{color: 'red'}}>*</span>}</h4>
          <p className={css.sectionDescription}>
            Upload your DOT Medical Card and related documents.
            {medicalRequirements.dotRequired && <span style={{color: 'red', fontWeight: 'bold'}}> (Required based on your medical responses)</span>}
          </p>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload DOT Medical Card:"
              maxFiles={2}
              onUploadComplete={(files) => formik.setFieldValue('dotMedicalCardFiles', files)}
              initialFiles={formik.values.dotMedicalCardFiles}
            />
            {formik.touched.dotMedicalCardFiles && formik.errors.dotMedicalCardFiles && (
              <p className={css.error}>
                {typeof formik.errors.dotMedicalCardFiles === 'string'
                  ? formik.errors.dotMedicalCardFiles
                  : 'DOT Medical Card is required'}
              </p>
            )}
          </div>
        </div>

        {/* TWIC Card */}
        <div className={css.section}>
          <h4>TWIC Card <span style={{color: 'red'}}>*</span></h4>
          <p className={css.sectionDescription}>
            Upload your Transportation Worker Identification Credential (TWIC) card.
            <span style={{color: 'red', fontWeight: 'bold'}}> (Required)</span>
          </p>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload TWIC Card:"
              maxFiles={2}
              onUploadComplete={(files) => formik.setFieldValue('twicCardFiles', files)}
              initialFiles={formik.values.twicCardFiles}
            />
            {formik.touched.twicCardFiles && formik.errors.twicCardFiles && (
              <p className={css.error}>
                {typeof formik.errors.twicCardFiles === 'string'
                  ? formik.errors.twicCardFiles
                  : 'TWIC Card is required'}
              </p>
            )}
          </div>
        </div>

        {/* HAZMAT and Others */}
        <div className={css.section}>
          <h4>HAZMAT and Other Certifications <span style={{color: 'red'}}>*</span></h4>
          <p className={css.sectionDescription}>
            Upload HAZMAT endorsement and other relevant certifications.
            <span style={{color: 'red', fontWeight: 'bold'}}> (Required)</span>
          </p>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload HAZMAT and Other Certifications:"
              maxFiles={5}
              onUploadComplete={(files) => formik.setFieldValue('hazmatAndOthersFiles', files)}
              initialFiles={formik.values.hazmatAndOthersFiles}
            />
            {formik.touched.hazmatAndOthersFiles && formik.errors.hazmatAndOthersFiles && (
              <p className={css.error}>
                {typeof formik.errors.hazmatAndOthersFiles === 'string'
                  ? formik.errors.hazmatAndOthersFiles
                  : 'HAZMAT and other certifications are required'}
              </p>
            )}
          </div>
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 3: Medical)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save & Exit (Complete Later)"}
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save & Continue (To Step 5: Consents & Review) →"}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtDocuments;
