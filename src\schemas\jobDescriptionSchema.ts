import * as Yup from "yup";

const baseJobSchema = Yup.object({
  jobDescription: Yup.string().required("Please enter job description"),
  contactPersonEmail: Yup.string()
    .transform((value) => (value === "" ? undefined : value))
    .notRequired()
    .email("Please enter a valid email")
    .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter a valid email"),
  eeoConfirmed: Yup.array()
    .of(Yup.mixed<string | number>())
    .min(1, "Please accept to continue")
    .required("Please accept to continue"),
});

export const getJobFormSchema = (isDraft: boolean) => {
  return isDraft ? null : baseJobSchema;
};

export const getJobSchoolSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return baseJobSchema.shape({
    routeEnvironments: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one route requirement")
      .required("Please select at least one route requirement"),
  });
};