"use client";

import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import { VehicleChoolValues, VehicleProps } from "../../../../types/jobpostingform";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { truckSchoolValues } from "@/initialValues/truckFormValues";
import { deepCleanValues, getCommonKeys, handleArrayFields, safeFetch, scrollToFirstError } from "@/utils/utils";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import OverlayScreen from "@/components/Common/Form/OverlayScreen";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import Textarea from "@/components/Common/Form/Textarea";
import { truckSchoolFormPayload } from "@/submitHandler/truckSubmit";
import { getTruckSchoolSchema } from "@/schemas/truckFormSchema";
import { experienceStudentsReq } from "@/utils/constant";

const TruckDetails = ({ formFields, setCurrentStep }: VehicleProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<VehicleChoolValues>({
    initialValues: truckSchoolValues,
    validationSchema: getTruckSchoolSchema(isDraft),

    onSubmit: async(values: VehicleChoolValues) => {
      setLoading(true);
      const payload = truckSchoolFormPayload(values, isDraft);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 4, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });

 useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost) {
        // getting common keys by removing all other keys from api
        const initialKeys = getCommonKeys(truckSchoolValues, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);
        
        const arrayFields = handleArrayFields(cleanedJobPost, {
          vehicleType: { outputKey: "otherVehicleText" },
          vehicleFeatures: { outputKey: "vehicleFeaturesText" },
          specialEquipmentDriverOperate: { outputKey: "specialEquipmentText", addField: "other" },
          studentAgeGroup: {},
          needsSupported: {},
          typicalRoute: {}
        });

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields
        });
      }
    };

    fetchData();
  }, [])

  console.log(formik.values)

  return (
    <div className={styles.payStructureInfo}>
      {loading && <OverlayScreen />}
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Bus Type & Features</h2>
          <CheckboxField 
            label="Primary Bus Type(s) Used for this Role"
            desc=" - Check all that apply"
            fieldName="vehicleType"
            formik={formik}
            checkboxArray={formFields?.["vehicle-type-job-posting-school-bus-driver"]}
          />
          {formik.values.vehicleType.includes(723) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="otherVehicleText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <RadioField
            label="Number of Passengers"
            fieldName="numberOfPassengers"
            formik={formik}
            radioArray={formFields?.["passenger-capacity-job-posting-school-bus-driver"]}
          />
          <CheckboxField 
            label="Bus Features"
            desc=" - Check all that apply"
            fieldName="vehicleFeatures"
            formik={formik}
            checkboxArray={formFields?.["vehicle-features-job-posting-school-bus-driver"]}
            hide={true}
          />
          {formik.values.vehicleFeatures.includes(741) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="vehicleFeaturesText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <CheckboxField 
            label="Special Equipment Driver Must Operate"
            desc=" - Check all that apply"
            fieldName="specialEquipmentDriverOperate"
            formik={formik}
            checkboxArray={[
              ...(formFields?.["driving-environments-job-posting-school-bus-driver"] || []),
              { label: "Other Medical Equipment Securement", value: "other"}
            ]}
          />
          {formik.values.specialEquipmentDriverOperate.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="specialEquipmentText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Student Population</h2>
          <CheckboxField 
            label="Primary Student Age Group(s) Transported"
            desc=" - Check all that apply"
            fieldName="studentAgeGroup"
            formik={formik}
            checkboxArray={formFields?.["grade-levels-job-posting-school-bus-driver"]}
          />
          <RadioField
            label="Experience with Special Needs Students Required/Preferred?"
            fieldName="experienceWithSpecialNeedsStudents"
            formik={formik}
            radioArray={experienceStudentsReq}
            hide={true}
          />
          {formik.values.experienceWithSpecialNeedsStudents === "" &&
            <CheckboxField 
              label="Types of Needs Supported"
              desc=" - Check all that apply"
              fieldName="needsSupported"
              formik={formik}
              checkboxArray={formFields?.["special-needs-experience-job-posting-school-bus-driver"]}
              hide={true}
            />
          }
          <CheckboxField 
            label="Typical Route Environment"
            desc=" - Check all that apply"
            fieldName="typicalRoute"
            formik={formik}
            checkboxArray={formFields?.["driving-environments-job-posting-school-bus-driver"]}
          />
          <Textarea
            label="Additional Route Information"
            fieldName="routeInfo"
            placeholder="e.g., Mix of paved and gravel roads; tight neighborhood turns; specific school protocols."
            formik={formik}
            hide={true}
          />
          <ButtonComponent
            backBtnHandler={() => setCurrentStep(2)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm()
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default TruckDetails;