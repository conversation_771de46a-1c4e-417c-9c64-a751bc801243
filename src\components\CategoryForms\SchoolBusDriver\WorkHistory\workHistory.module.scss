@import '../../../../styles/global.scss';

.workHistory {
    h3 {
        color: $black;
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;

        sup {
            color: #ff0000;
        }
    }

    h5 {
        color: $black;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        margin-top: 12px;
    }

    .required {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        margin-top: 16px;
        margin-bottom: 24px;

        sup {
            color: #ff0000;
        }
    }

    .note {
        border-radius: 8px;
        border: 1px solid #0075F2;
        background-color: #EDF6FF;
        padding: 20px;
        margin-top: 16px;

        h6 {
            color: #0075F2;
            font-weight: 700;
            font-size: 16px;
            line-height: 24px;
            text-transform: uppercase;
        }

        p {
            color: #0075F2;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            margin-top: 8px;
        }
    }

    .addPeriodBtn {
        border: none;
        border-radius: 8px;
        background-color: #555555;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        padding: 0px 20px;
        font-size: 14px;
        line-height: 20px;
        color: $white;
        cursor: pointer;
    }

    .drivingExperienceForm {
        margin-top: 28px;
    }

    .card {
        border: 1px solid #DDDDDD;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 28px;
        position: relative;

        h3 {
            margin-bottom: 16px;
        }

        h5 {
            margin-bottom: 16px;
        }

        .removeHistory {
            background-color: transparent;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: absolute;
            right: 24px;
            top: 24px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
            cursor: pointer;
        }
    }

    .formRow {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        margin-bottom: 28px;
        position: relative;

        &:last-child {
            margin-bottom: 0px;
        }

        &.dBlaco {
            display: block;
        }

        &.mb0 {
            margin-bottom: 0px;
        }

        .col01 {
            width: 100%;
        }

        .col02 {
            width: calc((100% - 24px) / 2);

            div {
                max-width: 100%;
            }
        }

        .col03 {
            width: calc((100% - 48px) / 3);

            div {
                max-width: 100%;
            }
        }

        .flex {
            display: flex;
            flex-wrap: wrap;
            column-gap: 24px;
            row-gap: 28px;
        }

        .error {
            color: #F91313;
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            margin-top: 4px;
        }
    }

    .labelDiv {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        width: 100%;

        &.mb8 {
            margin-bottom: 8px;
        }

        button {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 21px;
            cursor: pointer;
            margin-left: auto;
        }
    }

    label {
        display: block;
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.mb16 {
            margin-bottom: 16px;
        }

        span {
            font-weight: 400;
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    input[type='text'],
    input[type='email'],
    input[type='password'],
    input[type='tel'],
    input[type='number'],
    input[type='date'],
    input[type='url'],
    select,
    .dropdownToggle,
    textarea {
        background-color: #FFFFFF;
        border: 1px solid #707070;
        border-radius: 4px;
        width: 100%;
        height: 44px;
        color: #515B6F;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        text-align: left;
        padding: 10px 8px;
        outline: none !important;

        &:disabled {
            background-color: #F7F7F7;
            font-style: italic;
        }

        &::placeholder {
            color: #707070;
            font-weight: 400;
            opacity: 1;
        }
    }

    input[type='number'] {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
    }

    textarea {
        height: 88px;
        resize: none;
    }

    .characterLimit {
        color: $black;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 0%;
        display: block;
        margin-top: 4px;
    }

    .dropdown {
        position: relative;

        .dropdownToggle {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            background-image: url(/images/icons/icon-down-arrow.svg);
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .dropdownMenu {
            background-color: #FFFFFF;
            border-radius: 4px;
            -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            max-height: 180px;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
            width: 100%;
            left: 0px;
            top: calc(100% + 8px);
            z-index: 1;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: #F5F5F5; 
        }

        &::-webkit-scrollbar-thumb {
            background: #929292; 
            border-radius: 24px;
        }
    }

    .dropdownItem {
        background-color: #FFFFFF;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 15px;
        line-height: 22px;
        height: 44px;
        padding: 4px 12px;
        width: 100%;
    }

    .checkBox {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        &.column {
            flex-direction: column;
        }

        li {
            position: relative;
            user-select: none;
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 24px;
            width: 24px;
            z-index: 3;

            &:checked {
                ~.checkmark {
                    background-color: #555555;
                    border-color: #555555;

                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            height: 20px;
            width: 20px;
            background-color: #FFFFFF;
            border-radius: 2px;
            border: 2px solid #555555;
            position: absolute;
            left: 0px;
            top: 2px;

            &:after {
                content: "";
                position: absolute;
                display: none;
                left: 5px;
                top: 0px;
                width: 5px;
                height: 10px;
                border: solid $white;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            padding-left: 28px;
            margin: 0px;
            text-align: left;

            a {
                color: #0075F2;
            }
        }
    }

    .radioList {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .radioGroup {
            position: relative;
            padding-left: 32px;
            cursor: pointer;
            user-select: none;
            width: calc((100% - 64px) / 5);

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                width: 100%;
                height: 100%;
                left: 0px;
                z-index: 3;

                &:checked {
                    ~ .checkmark {
                        &:after {
                            display: block;
                        }
                    }
                }
            }

            .checkmark {
                border: 2px solid #555555;
                background-color: $white;
                border-radius: 50%;
                position: absolute;
                top: 0;
                left: 0;
                height: 20px;
                width: 20px;

                &:after {
                    background-color: #555555;
                    content: "";
                    position: absolute;
                    display: none;
                    top: 3px;
                    left: 3px;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                }
            }

            p {
                color: #555555;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
            }
        }
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 260px;
            }
        }
    }
}