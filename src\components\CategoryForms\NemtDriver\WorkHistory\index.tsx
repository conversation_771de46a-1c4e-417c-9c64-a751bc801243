"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchNemtDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import { FormikErrors, FormikTouched } from "formik";
import { getStates, State } from "@/services/locationService";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtWorkHistory.module.scss';

interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface WorkPeriod {
  id: string;
  periodType: string;
  companyName: string;
  address: Address;
  phone: string;
  position: string;
  subjectToTesting: string;
  operatedCdl: string;
  mayContact: string;
  brokersPlatforms: string;
  verificationContact: string;
  verificationPhone: string;
  startDate: Date | null;
  endDate: Date | null;
  currentlyWorking: boolean;
  reasonForLeaving: string;
  explanation: string;
}

interface WorkHistoryFormValues {
  workHistory: WorkPeriod[];
}

const NemtWorkHistory: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [states, setStates] = useState<State[]>([]);
  const [periodTypeOptions, setPeriodTypeOptions] = useState<FormValue[]>([]);
  const [reasonOptions, setReasonOptions] = useState<FormValue[]>([]);

  const createEmptyWorkPeriod = (): WorkPeriod => ({
    id: Date.now().toString(),
    periodType: "",
    companyName: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    phone: "",
    position: "",
    subjectToTesting: "unsure",
    operatedCdl: "no",
    mayContact: "yes",
    brokersPlatforms: "",
    verificationContact: "",
    verificationPhone: "",
    startDate: null,
    endDate: null,
    currentlyWorking: false,
    reasonForLeaving: "",
    explanation: "",
  });

  const validationSchema = Yup.object({
    workHistory: Yup.array()
      .of(
        Yup.object({
          periodType: Yup.string().required("Period type is required"),

          // Company name - required for all employment types except unemployment
          companyName: Yup.string().when("periodType", {
            is: (val: string) => val && !val.toLowerCase().includes("unemployment"),
            then: (schema) => schema.required("Company/Agency name is required"),
            otherwise: (schema) => schema,
          }),

          // Address fields - required for company employees (not independent contractors or unemployment)
          address: Yup.object({
            street: Yup.string().when("periodType", {
              is: (val: string) => val &&
                !val.toLowerCase().includes("unemployment") &&
                !val.toLowerCase().includes("independent") &&
                !val.toLowerCase().includes("contractor") &&
                !val.toLowerCase().includes("broker"),
              then: (schema) => schema.required("Street address is required"),
              otherwise: (schema) => schema,
            }),
            city: Yup.string().when("periodType", {
              is: (val: string) => val &&
                !val.toLowerCase().includes("unemployment") &&
                !val.toLowerCase().includes("independent") &&
                !val.toLowerCase().includes("contractor") &&
                !val.toLowerCase().includes("broker"),
              then: (schema) => schema.required("City is required"),
              otherwise: (schema) => schema,
            }),
            state: Yup.string().when("periodType", {
              is: (val: string) => val &&
                !val.toLowerCase().includes("unemployment") &&
                !val.toLowerCase().includes("independent") &&
                !val.toLowerCase().includes("contractor") &&
                !val.toLowerCase().includes("broker"),
              then: (schema) => schema.required("State is required"),
              otherwise: (schema) => schema,
            }),
            zipCode: Yup.string().when("periodType", {
              is: (val: string) => val &&
                !val.toLowerCase().includes("unemployment") &&
                !val.toLowerCase().includes("independent") &&
                !val.toLowerCase().includes("contractor") &&
                !val.toLowerCase().includes("broker"),
              then: (schema) => schema.required("Zip code is required"),
              otherwise: (schema) => schema,
            }),
          }),

          // Phone - required for company employees only
          phone: Yup.string().when("periodType", {
            is: (val: string) => val &&
              !val.toLowerCase().includes("unemployment") &&
              !val.toLowerCase().includes("independent") &&
              !val.toLowerCase().includes("contractor") &&
              !val.toLowerCase().includes("broker"),
            then: (schema) => schema.required("Company phone number is required"),
            otherwise: (schema) => schema,
          }),

          // Position - required for all employment types except unemployment
          position: Yup.string().when("periodType", {
            is: (val: string) => val && !val.toLowerCase().includes("unemployment"),
            then: (schema) => schema.required("Position is required"),
            otherwise: (schema) => schema,
          }),

          // Contact permission - required for company employees only
          mayContact: Yup.string().when("periodType", {
            is: (val: string) => val &&
              !val.toLowerCase().includes("unemployment") &&
              !val.toLowerCase().includes("independent") &&
              !val.toLowerCase().includes("contractor") &&
              !val.toLowerCase().includes("broker"),
            then: (schema) => schema.required("Contact permission is required"),
            otherwise: (schema) => schema,
          }),

          // Dates - required for all period types
          startDate: Yup.date().nullable().required("Start date is required"),
          endDate: Yup.date().nullable().when("currentlyWorking", {
            is: false,
            then: (schema) => schema.required("End date is required")
              .test("end-after-start", "End date must be after start date", function(value) {
                const { startDate } = this.parent;
                if (!startDate || !value) return true;
                return new Date(value) > new Date(startDate);
              }),
            otherwise: (schema) => schema.nullable(), // End date is optional when currently working
          }),

          // Reason for leaving - required for all period types
          reasonForLeaving: Yup.string().required("Reason for leaving is required"),

          // Explanation - required when specific reasons are selected
          explanation: Yup.string().when(["reasonForLeaving", "periodType"], {
            is: (reasonForLeaving: string, periodType: string) => {
              return (reasonForLeaving && (
                reasonForLeaving.toLowerCase().includes("other") ||
                reasonForLeaving.toLowerCase().includes("terminated") ||
                reasonForLeaving.toLowerCase().includes("unemployment")
              )) || (periodType && periodType.toLowerCase().includes("unemployment"));
            },
            then: (schema) => schema.required("Explanation is required").max(250, "Maximum 250 characters"),
            otherwise: (schema) => schema.max(250, "Maximum 250 characters"),
          }),
        })
      )
      .min(1, "At least one work period is required")
      .min(1, "At least one work period is required")
      .required("Work history is required")
      .test("coverage-check", "Work history must cover at least the last 36 months", function(workHistory) {
        if (!workHistory || workHistory.length === 0) return false;

        // Check if periods cover at least 36 months from today
        const today = new Date();
        const threeYearsAgo = new Date(today.getFullYear() - 3, today.getMonth(), today.getDate());

        console.log("Today:", today.toDateString());
        console.log("Three years ago:", threeYearsAgo.toDateString());

        // Check for future dates first
        const futurePeriods = workHistory.filter(period => {
          if (!period.startDate) return false;
          const startDate = new Date(period.startDate);
          return startDate > today;
        });

        if (futurePeriods.length > 0) {
          console.log("Found future periods:", futurePeriods.map(p => new Date(p.startDate!).toDateString()));
          return this.createError({
            message: "Work history cannot include future dates. Please use dates from the past only.",
          });
        }

        // Filter out periods without start dates and sort by start date
        const validPeriods = workHistory
          .filter(period => period.startDate !== null)
          .sort((a, b) => new Date(a.startDate!).getTime() - new Date(b.startDate!).getTime());

        console.log("Valid periods (excluding future):", validPeriods.map(p => {
          const period = p as WorkPeriod;
          return {
            start: new Date(period.startDate!).toDateString(),
            end: period.currentlyWorking ? "Present" : (period.endDate ? new Date(period.endDate).toDateString() : "No end date")
          };
        }));

        if (validPeriods.length === 0) {
          console.log("No valid periods found");
          return false;
        }

        const earliestStart = new Date(validPeriods[0].startDate!);
        const coverageValid = earliestStart <= threeYearsAgo;

        console.log("Earliest start:", earliestStart.toDateString());
        console.log("Coverage valid:", coverageValid);

        return coverageValid;
      })
      .test("date-overlap-check", "Work periods cannot overlap in dates", function(workHistory) {
        if (!workHistory || workHistory.length <= 1) return true;

        // Sort periods by start date
        const sortedPeriods = workHistory
          .filter(period => period.startDate !== null)
          .sort((a, b) => new Date(a.startDate!).getTime() - new Date(b.startDate!).getTime());

        // Check for overlaps
        for (let i = 0; i < sortedPeriods.length - 1; i++) {
          const currentPeriod = sortedPeriods[i] as WorkPeriod;
          const nextPeriod = sortedPeriods[i + 1] as WorkPeriod;

          const currentEnd = currentPeriod.currentlyWorking ? new Date() : currentPeriod.endDate;
          const nextStart = new Date(nextPeriod.startDate!);

          if (currentEnd && nextStart && new Date(currentEnd) > nextStart) {
            return this.createError({
              message: `Period ${i + 1} and ${i + 2} have overlapping dates`,
              path: `workHistory[${i}].endDate`
            });
          }
        }
        return true;
      }),
  });

  const formik = useFormik<WorkHistoryFormValues>({
    initialValues: {
      workHistory: [createEmptyWorkPeriod()],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load states and form field options
        const [statesData, formFieldsResponse] = await Promise.all([
          getStates(),
          fetchNemtDriverFormFields([
            "type-of-period-driver-school-bus-driver",
            "reason-for-leaving-ending-period-driver-school-bus-driver",
          ]),
        ]);

        setStates(statesData);

        if (formFieldsResponse && Object.keys(formFieldsResponse).length > 0) {
          const fields = formFieldsResponse;

          const periodTypeKey = "type-of-period-driver-school-bus-driver";
          if (fields[periodTypeKey]) {
            console.log("Period type options from API:", fields[periodTypeKey]);
            setPeriodTypeOptions(fields[periodTypeKey]);
          }

          const reasonKey = "reason-for-leaving-ending-period-driver-school-bus-driver";
          if (fields[reasonKey]) {
            setReasonOptions(fields[reasonKey]);
          }
        }


      } catch (error) {
        console.error("Error loading form fields:", error);
        toast.error("Failed to load form options. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Load existing employment history data when options are available
  useEffect(() => {
    const loadEmploymentHistory = async () => {
      if (periodTypeOptions.length > 0) {
        try {
          const driverDetails = await fetchDriverDetails();

          if (driverDetails?.status && driverDetails?.data?.driver?.driverEmploymentHistory) {
            const employmentHistory = driverDetails.data.driver.driverEmploymentHistory;
            console.log("📋 Loading existing employment history:", employmentHistory);
            console.log("📋 Available period type options:", periodTypeOptions);

            if (employmentHistory.length > 0) {
              const workPeriods: WorkPeriod[] = employmentHistory
                .sort((a: any, b: any) => (a.rank || 0) - (b.rank || 0))
                .map((emp: any) => {
                  console.log("🔄 Mapping employment period:", {
                    employerName: emp.employerName,
                    isUnemployment: emp.isUnemployment,
                    startDate: emp.startDate,
                    endDate: emp.endDate,
                    isCurrent: emp.isCurrent,
                    rank: emp.rank
                  });

                  // Determine period type based on unemployment status
                  let periodType = "";
                  if (emp.isUnemployment) {
                    // Find unemployment period option
                    const unemploymentOption = periodTypeOptions.find(option =>
                      option.label?.en?.toLowerCase().includes("unemployment") ||
                      option.formValueSlug?.toLowerCase().includes("unemployment")
                    );
                    periodType = unemploymentOption?.formValueSlug || "";
                    console.log("🔍 Found unemployment option:", unemploymentOption);
                  } else {
                    // Find employment period option (look for employment, nemt, or work-related terms)
                    const employmentOption = periodTypeOptions.find(option =>
                      option.label?.en?.toLowerCase().includes("employment") ||
                      option.label?.en?.toLowerCase().includes("nemt") ||
                      option.label?.en?.toLowerCase().includes("work") ||
                      option.formValueSlug?.toLowerCase().includes("employment") ||
                      option.formValueSlug?.toLowerCase().includes("nemt")
                    );
                    periodType = employmentOption?.formValueSlug || "";
                    console.log("🔍 Found employment option:", employmentOption);
                  }

                  console.log("✅ Selected period type:", periodType);

                  return {
                    id: emp.driverEmploymentHistoryId?.toString() || Date.now().toString(),
                    periodType: periodType,
                    companyName: emp.employerName || "",
                    address: {
                      street: emp.employerStreet || "",
                      city: emp.employerCity || "",
                      state: emp.employerState?.toString() || "",
                      zipCode: emp.employerZip || "",
                    },
                    phone: emp.employerPhone || "",
                    position: emp.positionHeld || "",
                    subjectToTesting: emp.subjectToFmcsa === true ? "yes" : emp.subjectToFmcsa === false ? "no" : "unsure",
                    operatedCdl: emp.operatedCmv ? "yes" : "no",
                    mayContact: emp.contactPermission ? "yes" : "no",
                    brokersPlatforms: "",
                    verificationContact: "",
                    verificationPhone: "",
                    startDate: emp.startDate ? new Date(emp.startDate) : null,
                    endDate: emp.endDate ? new Date(emp.endDate) : null,
                    currentlyWorking: emp.isCurrent || false,
                    reasonForLeaving: emp.reasonForLeaving || "",
                    explanation: emp.explanation || "",
                  };
                });

              console.log("✅ Mapped work periods:", workPeriods);
              formik.setFieldValue("workHistory", workPeriods);
            }
          } else {
            console.log("📋 No existing employment history found");
          }
        } catch (error) {
          console.error("Error loading employment history:", error);
        }
      }
    };

    loadEmploymentHistory();
  }, [periodTypeOptions]);

  // Fix invalid dropdown values when options are loaded
  useEffect(() => {
    if (periodTypeOptions.length > 0) {
      const updatedWorkHistory = formik.values.workHistory.map((period, index) => {
        const validatedPeriodType = validateDropdownValue(period.periodType, periodTypeOptions);
        if (validatedPeriodType !== period.periodType) {
          console.log(`Fixing invalid period type for index ${index}: "${period.periodType}" -> "${validatedPeriodType}"`);
          return { ...period, periodType: validatedPeriodType };
        }
        return period;
      });

      // Update form values if any changes were made
      const hasChanges = updatedWorkHistory.some((period, index) =>
        period.periodType !== formik.values.workHistory[index].periodType
      );

      if (hasChanges) {
        formik.setFieldValue('workHistory', updatedWorkHistory);
      }
    }
  }, [periodTypeOptions, formik.values.workHistory]);

  const handleSubmit = async (values: WorkHistoryFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      // If continuing to next step, validate the form first
      if (shouldContinue) {
        const errors = await formik.validateForm(values);
        if (Object.keys(errors).length > 0) {
          console.log("Validation errors:", errors);
          console.log("Form values:", values);

          // Show detailed error information
          if (errors.workHistory && Array.isArray(errors.workHistory)) {
            errors.workHistory.forEach((periodError: FormikErrors<WorkPeriod> | undefined, index: number) => {
              if (periodError) {
                console.log(`Period ${index + 1} errors:`, periodError);
              }
            });
          }

          formik.setTouched({
            workHistory: values.workHistory.map(() => ({
              periodType: true,
              companyName: true,
              address: {
                street: true,
                city: true,
                state: true,
                zipCode: true,
              },
              phone: true,
              position: true,
              mayContact: true,
              startDate: true,
              endDate: true,
              reasonForLeaving: true,
              explanation: true,
            }))
          });

          // Also mark the workHistory array itself as touched for form-level errors
          formik.setFieldTouched('workHistory', true);
          toast.error("Please fix all validation errors before continuing. Check console for details.");
          setIsLoading(false);
          return;
        }
      }

      const payload = {
        currentStage: 3,
        currentStep: 2,
        driver: {
          driverEmploymentHistory: values.workHistory.map((period, index) => ({
            typeOfEmployment: (() => {
              if (!period.periodType || period.periodType.toLowerCase().includes("unemployment")) {
                return undefined;
              }
              // Find the option by formValueSlug and return its formValueId
              const selectedOption = periodTypeOptions.find(option => option.formValueSlug === period.periodType);
              return selectedOption ? selectedOption.formValueId : null;
            })(),
            isUnemployment: period.periodType.toLowerCase().includes("unemployment"),
            employerName: !period.periodType.toLowerCase().includes("unemployment") ? period.companyName || undefined : undefined,
            employerStreet: !period.periodType.toLowerCase().includes("unemployment") ? period.address.street || undefined : undefined,
            employerCity: !period.periodType.toLowerCase().includes("unemployment") ? period.address.city || undefined : undefined,
            employerState: !period.periodType.toLowerCase().includes("unemployment") ? period.address.state || undefined : undefined,
            // employerManagerName: null, // Not collected in current form
            employerZip: !period.periodType.toLowerCase().includes("unemployment") ? period.address.zipCode || undefined : undefined,
            employerPhone: !period.periodType.toLowerCase().includes("unemployment") ? period.phone || undefined : undefined,
            // employerWebsite: null, // Not collected in current form
            positionHeld: period.position || undefined,
            subjectToFmcsa: period.subjectToTesting === "yes" ? true : period.subjectToTesting === "no" ? false : undefined,
            operatedCmv: period.operatedCdl === "yes" ? true : false,
            contactPermission: period.mayContact === "yes" ? true : false,
            startDate: period.startDate?.toISOString() || undefined,
            endDate: period.currentlyWorking ? undefined : period.endDate?.toISOString() || undefined,
            isCurrent: period.currentlyWorking,
            reasonForLeaving: period.reasonForLeaving || undefined,
            explanation: period.explanation || undefined,
            rank: index + 1
          }))
        }
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Work history saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save work history. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting work history:", error);
      toast.error("Failed to save work history. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const addWorkPeriod = () => {
    const newPeriod = createEmptyWorkPeriod();
    formik.setFieldValue("workHistory", [...formik.values.workHistory, newPeriod]);
  };

  const removeWorkPeriod = (index: number) => {
    if (formik.values.workHistory.length > 1) {
      const updatedHistory = formik.values.workHistory.filter((_, i) => i !== index);
      formik.setFieldValue("workHistory", updatedHistory);
    }
  };



  // Helper function to validate dropdown value against available options
  const validateDropdownValue = (value: string, options: FormValue[]): string => {
    if (!value) return "";
    const validOption = options.find(option => option.formValueSlug === value);
    if (validOption) return value;

    console.warn(`Invalid dropdown value "${value}" not found in options:`, options.map(o => o.formValueSlug));
    return "";
  };

  // Helper function to get error message safely
  const getFieldError = (fieldPath: string, index: number): string | undefined => {
    const errors = formik.errors.workHistory;
    const touched = formik.touched.workHistory;

    if (!Array.isArray(errors) || !Array.isArray(touched)) return undefined;
    if (!errors[index] || !touched[index]) return undefined;

    const fieldParts = fieldPath.split('.');
    let errorValue: FormikErrors<WorkPeriod> | string | undefined = errors[index];
    let touchedValue: FormikTouched<WorkPeriod> | boolean | undefined = touched[index];

    for (const part of fieldParts) {
      if (typeof errorValue === 'object' && errorValue !== null) {
        errorValue = (errorValue as Record<string, unknown>)[part] as FormikErrors<WorkPeriod> | string | undefined;
      }
      if (typeof touchedValue === 'object' && touchedValue !== null) {
        touchedValue = (touchedValue as Record<string, unknown>)[part] as FormikTouched<WorkPeriod> | boolean | undefined;
      }
    }

    return touchedValue && errorValue ? String(errorValue) : undefined;
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.workHistory}>
      <h3>Step 2: Work History (Last 3 Years Required)</h3>
      <div className={css.note}>
        <h6>Important:</h6>
        <p>Provide a complete history for the <strong>past 3 years</strong>. Include all NEMT driving roles, other employment, and any unemployment periods. Verification is important.</p>
      </div>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        <div className={css.workHistorySection}>
          <label>Work / Unemployment Periods (Start with most recent)</label>
          <button
            type="button"
            onClick={addWorkPeriod}
            className={css.addPeriodBtn}
          >
            + Add Work/Unemployment Period
          </button>

          {/* Form-level validation errors */}
          {formik.errors.workHistory && typeof formik.errors.workHistory === 'string' && formik.touched.workHistory && (
            <div className={css.formLevelError} style={{
              marginTop: '10px',
              padding: '10px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '4px'
            }}>
              <p className={css.error} style={{
                margin: '0',
                fontWeight: 'bold',
                fontSize: '14px'
              }}>
                ⚠️ {formik.errors.workHistory}
              </p>
            </div>
          )}

          {formik.values.workHistory.map((period, index) => (
            <div key={period.id} className={css.periodBlock}>
              <h4>Period {index + 1}</h4>

              {/* Period Type */}
              <div className={css.formRow}>
                <label>Type of Period: *</label>
                <Dropdown
                  options={periodTypeOptions.map(option => ({ value: option.formValueSlug, label: option.label.en }))}
                  value={validateDropdownValue(period.periodType, periodTypeOptions)}
                  placeholder="Select Type"
                  onChange={(value) => {
                    console.log(`Setting period type for index ${index}:`, value);
                    formik.setFieldValue(`workHistory[${index}].periodType`, value);
                    // Force a re-render by updating the form values
                    setTimeout(() => {
                      console.log(`Period type after setting:`, formik.values.workHistory[index].periodType);
                    }, 100);
                  }}
                  error={getFieldError('periodType', index)}
                  name={`workHistory[${index}].periodType`}
                />
               
              </div>

              {period.periodType && !period.periodType.toLowerCase().includes("unemployment") && (
                <>
                  {/* NEMT Driver (Independent Contractor / Working with Brokers) */}
                  {(period.periodType.toLowerCase().includes("independent") ||
                    period.periodType.toLowerCase().includes("contractor") ||
                    period.periodType.toLowerCase().includes("broker")) ? (
                    <>
                      <div className={css.formRow}>
                        <label>Primary Broker(s) / Platform(s) Worked With (Optional):</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].brokersPlatforms`}
                          value={period.brokersPlatforms}
                          onChange={formik.handleChange}
                          placeholder="e.g., Modivcare, MTM, Uber Health, Self-Employed"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Position Held: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].position`}
                          value="Independent Contractor / NEMT Driver"
                          className={css.input}
                          readOnly
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Were you subject to Drug & Alcohol Testing?</label>
                        <p className={css.subLabel}>(May depend on broker contracts)</p>
                        <RadioGroup
                          name={`workHistory[${index}].subjectToTesting`}
                          options={["yes", "no", "unsure"]}
                          selectedValue={period.subjectToTesting}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].subjectToTesting`, value)
                          }
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Verification Contact (Optional):</label>
                        <p className={css.subLabel}>Optional: Provide reference from broker or facility if desired.</p>
                        <div className={css.contactFields}>
                          <input
                            type="text"
                            name={`workHistory[${index}].verificationContact`}
                            value={period.verificationContact}
                            onChange={formik.handleChange}
                            placeholder="Contact Name"
                            className={css.input}
                          />
                          <input
                            type="tel"
                            name={`workHistory[${index}].verificationPhone`}
                            value={period.verificationPhone}
                            onChange={formik.handleChange}
                            placeholder="(*************"
                            className={css.input}
                          />
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* NEMT Driver (Company Employee) OR Other Employment Types */}
                      <div className={css.formRow}>
                        <label>Company / Agency Name: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].companyName`}
                          value={period.companyName}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Enter Company Name"
                          className={css.input}
                        />
                        {getFieldError('companyName', index) && (
                          <p className={css.error}>
                            {getFieldError('companyName', index)}
                          </p>
                        )}
                      </div>

                      <div className={css.formRow}>
                        <label>Company Full Address: *</label>
                        <div className={css.addressFields}>
                          <input
                            type="text"
                            name={`workHistory[${index}].address.street`}
                            value={period.address.street}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            placeholder="Street Address"
                            className={css.input}
                          />
                          <input
                            type="text"
                            name={`workHistory[${index}].address.city`}
                            value={period.address.city}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            placeholder="City"
                            className={css.input}
                          />
                          <Dropdown
                            options={states.map(state => ({ value: state.slug, label: state.name.en }))}
                            value={period.address.state}
                            placeholder="Select State"
                            onChange={(value) => formik.setFieldValue(`workHistory[${index}].address.state`, value)}
                            name={`workHistory[${index}].address.state`}
                          />
                          <input
                            type="text"
                            name={`workHistory[${index}].address.zipCode`}
                            value={period.address.zipCode}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            placeholder="Zip Code"
                            className={css.input}
                          />
                        </div>
                        {/* Address validation errors */}
                        {getFieldError('address.street', index) && (
                          <p className={css.error}>
                            {getFieldError('address.street', index)}
                          </p>
                        )}
                        {getFieldError('address.city', index) && (
                          <p className={css.error}>
                            {getFieldError('address.city', index)}
                          </p>
                        )}
                        {getFieldError('address.state', index) && (
                          <p className={css.error}>
                            {getFieldError('address.state', index)}
                          </p>
                        )}
                        {getFieldError('address.zipCode', index) && (
                          <p className={css.error}>
                            {getFieldError('address.zipCode', index)}
                          </p>
                        )}
                      </div>

                      <div className={css.formRow}>
                        <label>Company Phone Number (Supervisor/HR): *</label>
                        <p className={css.subLabel}>Required for verification.</p>
                        <input
                          type="tel"
                          name={`workHistory[${index}].phone`}
                          value={period.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="(*************"
                          className={css.input}
                        />
                        {getFieldError('phone', index) && (
                          <p className={css.error}>
                            {getFieldError('phone', index)}
                          </p>
                        )}
                      </div>

                      <div className={css.formRow}>
                        <label>Position Held: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].position`}
                          value={period.position}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="e.g., NEMT Driver, Patient Transporter, Scheduler"
                          className={css.input}
                        />
                        {getFieldError('position', index) && (
                          <p className={css.error}>
                            {getFieldError('position', index)}
                          </p>
                        )}
                      </div>

                      <div className={css.formRow}>
                        <label>Were you subject to Drug & Alcohol Testing in this role?</label>
                        <p className={css.subLabel}>(Often required for NEMT)</p>
                        <RadioGroup
                          name={`workHistory[${index}].subjectToTesting`}
                          options={["yes", "no", "unsure"]}
                          selectedValue={period.subjectToTesting}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].subjectToTesting`, value)
                          }
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Did you operate a vehicle requiring a CDL in this role?</label>
                        <p className={css.subLabel}>(Only if driving larger buses)</p>
                        <RadioGroup
                          name={`workHistory[${index}].operatedCdl`}
                          options={["yes", "no"]}
                          selectedValue={period.operatedCdl}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].operatedCdl`, value)
                          }
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>May we contact this Company for verification? *</label>
                        <RadioGroup
                          name={`workHistory[${index}].mayContact`}
                          options={["yes", "no"]}
                          selectedValue={period.mayContact}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].mayContact`, value)
                          }
                        />
                        {getFieldError('mayContact', index) && (
                          <p className={css.error}>
                            {getFieldError('mayContact', index)}
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </>
              )}

              {/* Dates (From - To) - shown for all period types */}
              <div className={css.formRow}>
                <label>Dates (From - To): *</label>
                <p className={css.subLabel}>
                  MM/DD/YYYY format. To date must be after From date. Periods must cover last 3 years.
                </p>
                <div className={css.dateFields}>
                  <div className={css.dateField}>
                    <DateInput
                      selected={period.startDate}
                      onChange={(date) => {
                        formik.setFieldValue(`workHistory[${index}].startDate`, date);
                        // Trigger validation for this field
                        setTimeout(() => {
                          formik.setFieldTouched(`workHistory[${index}].startDate`, true);
                        }, 100);
                      }}
                      placeholder="Start Date"
                    />
                  </div>
                  <div className={css.dateField}>
                    <DateInput
                      selected={period.endDate}
                      onChange={(date) => {
                        formik.setFieldValue(`workHistory[${index}].endDate`, date);
                        // Trigger validation for this field
                        setTimeout(() => {
                          formik.setFieldTouched(`workHistory[${index}].endDate`, true);
                        }, 100);
                      }}
                      disabled={period.currentlyWorking}
                      placeholder={period.currentlyWorking ? "Present" : "End Date or Present"}
                    />
                  </div>
                  <div className={css.checkboxField}>
                    <input
                      type="checkbox"
                      name={`workHistory[${index}].currentlyWorking`}
                      checked={period.currentlyWorking}
                      onChange={(e) => {
                        formik.setFieldValue(`workHistory[${index}].currentlyWorking`, e.target.checked);
                        if (e.target.checked) {
                          formik.setFieldValue(`workHistory[${index}].endDate`, null);
                        }
                      }}
                    />
                    <label>Currently working in this role</label>
                  </div>
                </div>
                {/* Date validation errors */}
                {getFieldError('startDate', index) && (
                  <p className={css.error}>
                    {getFieldError('startDate', index)}
                  </p>
                )}
                {getFieldError('endDate', index) && (
                  <p className={css.error}>
                    {getFieldError('endDate', index)}
                  </p>
                )}
              </div>

              {/* Reason for leaving */}
              <div className={css.formRow}>
                <label>Reason for Leaving / Ending Period: *</label>
                <Dropdown
                  options={reasonOptions.map(option => ({ value: option.formValueSlug, label: option.label.en }))}
                  value={period.reasonForLeaving}
                  placeholder="Select Reason"
                  onChange={(value) => formik.setFieldValue(`workHistory[${index}].reasonForLeaving`, value)}
                  name={`workHistory[${index}].reasonForLeaving`}
                />
                {getFieldError('reasonForLeaving', index) && (
                  <p className={css.error}>
                    {getFieldError('reasonForLeaving', index)}
                  </p>
                )}
              </div>

              {/* Brief Explanation - Required only if 'Other', 'Terminated', or 'Unemployment' selected */}
              {((period.reasonForLeaving && (
                  period.reasonForLeaving.toLowerCase().includes("other") ||
                  period.reasonForLeaving.toLowerCase().includes("terminated") ||
                  period.reasonForLeaving.toLowerCase().includes("unemployment")
                )) ||
                (period.periodType && period.periodType.toLowerCase().includes("unemployment"))) && (
                <div className={css.formRow}>
                  <label>Brief Explanation (Required only if Other, Terminated, or Unemployment selected): *</label>
                  <textarea
                    name={`workHistory[${index}].explanation`}
                    value={period.explanation}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Briefly explain reason if needed... Max 250 characters"
                    maxLength={250}
                    className={css.textarea}
                    rows={3}
                  />
                  {getFieldError('explanation', index) && (
                    <p className={css.error}>
                      {getFieldError('explanation', index)}
                    </p>
                  )}
                </div>
              )}

              {formik.values.workHistory.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeWorkPeriod(index)}
                  className={css.removeBtn}
                >
                  Remove This Period
                </button>
              )}
            </div>
          ))}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 1: Experience)
            </button>
          )}

          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={async () => {
                await handleSubmit(formik.values, false);
              }}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
              onClick={() => {
                console.log("Submit button clicked");
                console.log("Form is valid:", formik.isValid);
                console.log("Form errors:", formik.errors);
                console.log("Form values:", formik.values);
              }}
            >
              Save & Continue (To Step 3: Medical) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtWorkHistory;