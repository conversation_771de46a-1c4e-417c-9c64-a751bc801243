"use client";
import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import { booleanFlags, otherValue } from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import TextField from "../../../Common/Form/TextField";
import CheckboxField from "../../../Common/Form/CheckboxField";
import RadioField from "../../../Common/Form/RadioField";
import DropdownField from "@/components/Common/Form/DropdownField";
import { BasicProps, BasicFormValues } from "../../../../types/jobpostingform";
import { basicFormInitial } from "@/initialValues/basicFormValues";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import { deepCleanValues, getCommonKeys, handleArrayFields, numericInput, safeFetch, scrollToFirstError } from "@/utils/utils";
import DatePickerComponent from "@/components/Common/Form/DatePickerComponent";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { getBasicSchoolSchema } from "@/schemas/basicFormSchema";
import { basicFormPayload } from "@/submitHandler/basicFormSubmit";

const BasicForm = ({ states, formFields, setCurrentStep, companyDetails, setLoading }: BasicProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<BasicFormValues>({
    initialValues: basicFormInitial,
    validationSchema: getBasicSchoolSchema(isDraft),

    onSubmit: async(values: BasicFormValues) => {
      setLoading(true);
      const payload = basicFormPayload(values, isDraft, companyDetails);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 2, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost) {
        const initialKeys = getCommonKeys(basicFormInitial, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);

        console.log(initialKeys, "initialKeys")

        const arrayFields = handleArrayFields(cleanedJobPost, {
          routeType: { outputKey: "specificRoute", addField: "other" },
        });

        const newObject = {} as Record<string, unknown>;

        if (typeof cleanedJobPost.jobTitle === 'string' && !isNaN(Number(cleanedJobPost.jobTitle))) {
          newObject.jobTitle = Number(cleanedJobPost.jobTitle);
        } else {
          newObject.otherJob = cleanedJobPost.jobTitle;
          newObject.jobTitle = "other";
        }

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...newObject
        });
      }
    };

    fetchData();
  }, [])

  return (
    <div className={styles.basicFormInfo}>
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Position Details</h2>
          <div className={styles.rowField}>
            <DropdownField 
              className="columnWidth_3"
              label="Job Title"
              fieldName="jobTitle"
              defaultLabel="Select Job Title"
              formik={formik}
              tooltipMsg="Select the most fitting title for this role."
              dropdownArray={[ 
                ...(formFields?.["job-title-job-posting-cdl"] || []), 
                otherValue
              ]}
            />
            {(formik.values.jobTitle === "other") && 
              <TextField
                className="columnWidth_3"
                label="Specify Other Job Title"
                fieldName="otherJob"
                placeholder="Enter custom job title"
                formik={formik}
              />
            }
          </div> 
          <CheckboxField 
            label="Position Type"
            desc=" - Check all that apply"
            fieldName="employmentTypes"
            formik={formik}
            checkboxArray={formFields?.["employment-type-check-all-that-apply-job-posting-cdl"]}
          />
          <div className={`${styles.rowField} ${styles.marginAdjust}`}>
            <TextField
              className="columnWidth_3"
              styleClass={styles.tooltipLabel}
              label="Number of Identical Openings for This Position Type"
              fieldName="numberOfOpenings"
              formik={formik}
              handleChange={numericInput}
              tooltipMsg="Enter the number of drivers needed for this specific role."
            />
          </div>
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Location, Route & Schedule</h2>
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Primary Location / Terminal / Yard"
              fieldName="locationCity"
              placeholder="Enter City"
              formik={formik}
              tooltipMsg="City and State where the driver typically reports or starts routes."
            />
            <DropdownField 
              className = "columnWidth_3"
              label = "State"
              fieldName="stateId"
              defaultLabel="Select State"
              formik={formik}
              dropdownArray={states}
            />
            <TextField
              className="columnWidth_3"
              label="Zip Code"
              fieldName="locationZipCode"
              placeholder="Enter Primary Zip Code"
              formik={formik}
              hide={true}
              maxLength={5}
              handleChange={numericInput}
            />
          </div>
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Primary Service Area / Schools Covered"
              fieldName="specificServiceArea"
              placeholder="Schools in Example District serving Northern County"
              formik={formik}
              hide={true}
            />
          </div>
          <CheckboxField 
            label="Route Types Assigned"
            desc=" - Check all that apply"
            fieldName="routeType"
            formik={formik}
            checkboxArray={[ 
              ...(formFields?.["route-types-assigned-job-posting-school-bus-driver"] || []), 
              otherValue
            ]}
          />
          {formik.values.routeType.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="specificRoute"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <RadioField
            label="Does drive needs to be close to Location / Pickup / Route?"
            fieldName="hasPickUpLocation"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.hasPickUpLocation &&
            <>
              <TextField
                className="columnWidth_3"
                label="Enter Location"
                fieldName="firstPickUpLocation"
                placeholder="Zipcode"
                formik={formik}
                hide={true}
              />
              <RadioField
                styleClass={styles.radioMargin}
                fieldName="pickUpLocationDistance"
                formik={formik}
                radioArray={formFields?.["distance-range-job-posting-cdl"]}
                hide={true}
              />
            </>
          }
          <CheckboxField 
            label="Work Schedule Days"
            fieldName="workScheduleDays"
            formik={formik}
            checkboxArray={formFields?.["preferred-work-days-job-posting-school-bus-driver"]}
          />
          <CheckboxField 
            label="Work Schedule Timing"
            fieldName="workScheduleTime"
            formik={formik}
            checkboxArray={formFields?.["work-schedule-timing-job-posting-school-bus-driver"]}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Enter Specific Hours or Shift Details"
              fieldName="workScheduleShifts"
              placeholder="e.g., Approx 6:30-9:00 AM & 2:30-5:00 PM"
              formik={formik}
              hide={true}
            />
          </div>
          <RadioField
            label="How soon you can join?"
            fieldName="joinPeriod"
            formik={formik}
            radioArray={formFields?.["target-start-date-job-posting-cdl"]}
          />
          <DatePickerComponent
            className="columnWidth_3"
            label="Target Joining Date"
            desc=" (MM/DD/YYYY)"
            fieldName="targetStartDate"
            formik={formik}
            hide={true}
          />
          <ButtonComponent
            className="rightAlign"
            backText=""
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm();
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default BasicForm;
