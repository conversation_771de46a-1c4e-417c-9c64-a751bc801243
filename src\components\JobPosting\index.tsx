"use client";
import React, { useEffect, useState } from "react";
import CompanyWithStepDetails from "./CompanyWithStepDetails";
import styles from "./jobPosting.module.scss";
import { JobPostingProps } from "../../types/jobpostingform";
import { getSteps } from "./CompanyWithStepDetails/DynamciStepData";
import OverlayScreen from "../Common/Form/OverlayScreen";

const FormStructure = ({
  lang,
  formFields,
  currentStepNo,
  languages,
  otherLanguages,
  companyDetails,
  states,
  jobCategoryId
}: JobPostingProps) => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [currentStep]);

  const stepData = getSteps({
    jobCategoryId,
    lang,
    formFields,
    setCurrentStep,
    companyDetails,
    states,
    languages,
    otherLanguages,
    setLoading,
    currentStepNo
  });

  return (
    <div className={styles.formStepContainer}>
      {loading && <OverlayScreen />}
      <CompanyWithStepDetails 
        currentStep={currentStep} 
        steps={stepData} 
        jobCategoryId={jobCategoryId}
      />
      {stepData[currentStep - 1]?.component}
    </div>
  );
};

export default FormStructure;
