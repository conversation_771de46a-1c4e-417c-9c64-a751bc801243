import * as Yup from "yup";

export const getTruckFormSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    vehicleType: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one truck type")
      .required("Please select at least one truck type"),
    otherVehicleText: Yup.string().when("vehicleType", {
      is: (type: (string | number)[]) => type.includes(301),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    truckAge: Yup.mixed<string | number>().required("Please select truck age"),
    transmission: Yup.mixed<string | number>().required(
      "Please select transmission type"
    ),
    truckAssignment: Yup.mixed<string | number>().required(
      "Please select truck assignment"
    ),
    trailerTypes: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one trailer type")
      .required("Please select at least one trailer type"),
    truckAmenitiesText: Yup.string().when("truckAmenities", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    inCabTechText: Yup.string().when("inCabTech", {
      is: (type: (string | number)[]) => type.includes(328),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    eldSystemsText: Yup.string().when("eldSystems", {
      is: (type: (string | number)[]) => type.includes(336),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    otherTrailer: Yup.string().when("trailerTypes", {
      is: (type: (string | number)[]) => type.includes(355),
      then: (schema) => schema.required("Please enter other trailer type"),
      otherwise: (schema) => schema.notRequired(),
    }),
    freightTypes: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one freight type")
      .required("Please select at least one freight type"),
    freightTypesText: Yup.string().when("freightTypes", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    loadingReqs: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one requirement")
      .required("Please select at least one requirement"),
  });
};

export const getTruckSchoolSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    vehicleType: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one bus type")
      .required("Please select at least one bus type"),
    otherVehicleText: Yup.string().when("vehicleType", {
      is: (type: (string | number)[]) => type.includes(723),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    numberOfPassengers: Yup.mixed<string | number>().required("Please select number of passengers"),
    specialEquipmentDriverOperate: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one special equipment")
      .required("Please select at least one special equipment"),
    specialEquipmentText: Yup.string().when("specialEquipmentDriverOperate", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    studentAgeGroup: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one student age group")
      .required("Please select at least one student age group"),
    typicalRoute: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one typical route")
      .required("Please select at least one typical route"),
  });
};

