export const payFormInitial = {
  type: "",
  startingCpm: "",
  cpmRangeMin: "",
  cpmRangeMax: "",
  mileageCalculationMethod: "",
  startingHourlyRate: "",
  hourlyRateRangeMin: "",
  hourlyRateRangeMax: "",
  overtimeOption: "",
  overtimeAfterHours: "",
  payPerDay: "",
  typicalHoursPerDay: "",
  payPerWeek: "",
  typicalHoursDaysWeek: "",
  percentageRate: "",
  percentageBasedOn: "",
  payPerLoad: "",
  additionalPayFactors: "",
  annualSalary: "",
  salaryRangeMin: "",
  salaryRangeMax: "",
  combinationDescription: "",
  minimumGuaranteedPay: "",
  minimumGuaranteedPayPeriod: "",
  averageEarningsEstimate: "",
  paySchedule: "",
  signOnBonusOffered: "",
  signOnBonusAmount: "",
  signOnBonusPayoutStructure: "",
  detentionPayRate: "",
  detentionAfterHours: "",
  layoverPay: "",
  stopPayPerStop: "",
  stopPayExcludeFirstLast: [],
  otherBonusDescription: "",
  benefitsPackage: "",
  benefits: [],
  driverPerksAndPrograms: [],
  orientationRequired: "",
  orientationDuration: "",
  orientationDurationUnit: "",
  orientationLocation: "",
  orientationExpense: [],
  orientationPayRate: "",
  orientationPayUnit: "",
};

export const payFormValues = {
  ...payFormInitial,
  averageEarningsEstimate1: "",
  estimatePeriod: "",
  bonusOffered: [],
  otherBenefit: "",
  paidOrientation: "",

};

export const paySchoolFormValues = {
  ...payFormValues,
  nonDrivingTime: "",
  nonDrivingPay: [],
  otherPayText: "",
  payBonusOffered: ""
};

