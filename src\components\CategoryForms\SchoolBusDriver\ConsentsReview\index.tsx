"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchSchoolBusDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails
} from "@/services/driverFormService";
import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import css from './consentsReview.module.scss';

interface ConsentsReviewFormValues {
  consentShareProfile: boolean;
  consentBackgroundCheck: boolean;
  consentClearinghouse: boolean;
  consentCertifyInfo: boolean;
  consentAcceptTerms: boolean;
  availability: string;
  availabilitySpecificDate: Date | null;
  employmentType: string[];
  preferredRouteType: string[];
  workSplitShift: string;
}

const ConsentsReview: React.FC = () => {
  const { updateStepFromApiResponse, driverData, goToPreviousStep, canGoBack, setCurrentStep } = useSchoolBusDriverCategory();
  const router = useRouter();
  const params = useParams();
  const lang = params?.lang as string;

  const handleEditStep = (step: number) => {
    setCurrentStep(step);
  }
  
  const [isLoading, setIsLoading] = useState(true);
  const [availabilityOptions, setAvailabilityOptions] = useState<FormValue[]>([]);
  const [employmentTypeOptions, setEmploymentTypeOptions] = useState<FormValue[]>([]);
  const [routeTypeOptions, setRouteTypeOptions] = useState<FormValue[]>([]);

  // Load form field options first
  useEffect(() => {
    const loadFormFields = async () => {
      try {
        const formFields = await fetchSchoolBusDriverFormFields();

        const availability = (formFields["when-are-you-available-to-start-driver-cdl"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");

        const employmentTypes = (formFields["preferred-employment-types-driver-cdl"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");

        const routeTypes = (formFields["preferred-route-types-driver-cdl"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");

        setAvailabilityOptions(availability);
        setEmploymentTypeOptions(employmentTypes);
        setRouteTypeOptions(routeTypes);

        console.log("🔍 Form fields loaded:", {
          availability: availability.length,
          employmentTypes: employmentTypes.length,
          routeTypes: routeTypes.length
        });
      } catch (err: unknown) {
        console.error("Failed to fetch form fields:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to load form fields.";
        toast.error(errorMessage);
      }
    };

    loadFormFields();
  }, []);

  const initialValues: ConsentsReviewFormValues = {
    consentShareProfile: false,
    consentBackgroundCheck: false,
    consentClearinghouse: false,
    consentCertifyInfo: false,
    consentAcceptTerms: false,
    availability: "",
    availabilitySpecificDate: null,
    employmentType: [],
    preferredRouteType: [],
    workSplitShift: "",
  };

  const validationSchema = Yup.object().shape({
    consentShareProfile: Yup.boolean().oneOf([true], "Profile sharing consent is required"),
    consentBackgroundCheck: Yup.boolean().oneOf([true], "Background check consent is required"),
    consentClearinghouse: Yup.boolean().oneOf([true], "FMCSA Clearinghouse consent is required"),
    consentCertifyInfo: Yup.boolean().oneOf([true], "Certification of truthfulness is required"),
    consentAcceptTerms: Yup.boolean().oneOf([true], "Terms & Privacy agreement is required"),
    availability: Yup.string().required("Date available for work is required"),
    availabilitySpecificDate: Yup.date().nullable().when("availability", {
      is: (val: string) => {
        const option = availabilityOptions.find(opt => opt.formValueId.toString() === val);
        const isSpecificDate = option?.label.en.toLowerCase().includes("specific date");
        console.log("Validation - availability:", val, "isSpecificDate:", isSpecificDate);
        return isSpecificDate;
      },
      then: (schema) => schema.required("Specific date is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
    employmentType: Yup.array().min(1, "At least one employment type is required"),
    workSplitShift: Yup.string().required("Split shift preference is required"),
  });

  const formik = useFormik<ConsentsReviewFormValues>({
    initialValues,
    enableReinitialize: true,
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  // Load driver details after form fields and formik are ready
  useEffect(() => {
    const loadDriverDetails = async () => {
      // Wait for form field options to be loaded
      if (availabilityOptions.length === 0 || employmentTypeOptions.length === 0 || routeTypeOptions.length === 0) {
        console.log("🔍 Form options not loaded yet, skipping driver details load");
        return;
      }

      try {
        const detailsResponse = await fetchDriverDetails();
        console.log("🔍 Driver details response:", detailsResponse);

        const driver = detailsResponse?.data?.driver;
        console.log("🔍 Driver data:", driver);

        if (driver) {
          const cleanedValues: ConsentsReviewFormValues = {
            consentShareProfile: Boolean(driver.consentShareProfile),
            consentBackgroundCheck: Boolean(driver.consentBackgroundCheck),
            consentClearinghouse: Boolean(driver.consentClearinghouse),
            consentCertifyInfo: Boolean(driver.consentCertifyInfo),
            consentAcceptTerms: Boolean(driver.consentAcceptTerms),
            availability: driver.availability?.toString() || "",
            availabilitySpecificDate: driver.availabilitySpecificDate ? new Date(driver.availabilitySpecificDate) : null,
            employmentType: driver.employmentType?.map((id: number) => id.toString()) || [],
            preferredRouteType: driver.preferredRouteType?.map((id: number) => id.toString()) || [],
            workSplitShift: driver.workSplitShift === true ? "0" : driver.workSplitShift === false ? "1" : "1",
          };

          console.log("🔍 Pre-populating consent form with existing data:", cleanedValues);
          console.log("🔍 Available options:", {
            availability: availabilityOptions.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
            employmentTypes: employmentTypeOptions.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
            routeTypes: routeTypeOptions.map(opt => ({ id: opt.formValueId, label: opt.label.en }))
          });

          formik.setValues(cleanedValues);
          console.log("🔍 After setting formik values:", formik.values);
        }

        setIsLoading(false);
      } catch (err: unknown) {
        console.error("Failed to fetch driver details:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to load driver data.";
        toast.error(errorMessage);
        setIsLoading(false);
      }
    };

    loadDriverDetails();
  }, [availabilityOptions, employmentTypeOptions, routeTypeOptions, formik.setValues]);

  const handleSubmit = async (values: ConsentsReviewFormValues, shouldContinue: boolean = true) => {
    console.log("Consents & Review form submission started with values:", values);
    
    setIsLoading(true);
    try {
      const payload = {
        currentStage: 3,
        currentStep: 5,
        driver: {
          consentShareProfile: values.consentShareProfile,
          consentBackgroundCheck: values.consentBackgroundCheck,
          consentClearinghouse: values.consentClearinghouse,
          consentCertifyInfo: values.consentCertifyInfo,
          consentAcceptTerms: values.consentAcceptTerms,
          availability: values.availability ? parseInt(values.availability) : undefined,
          availabilitySpecificDate: values.availabilitySpecificDate ? values.availabilitySpecificDate.toISOString() : null,
          employmentType: values.employmentType.map(id => parseInt(id)),
          preferredRouteType: values.preferredRouteType.map(id => parseInt(id)),
          workSplitShift: values.workSplitShift === "1"
            ? true
            : values.workSplitShift === "0"
            ? false
            : null,
        },
      };

      console.log("Submitting consents & review payload:", JSON.stringify(payload, null, 2));
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Profile completed successfully!");
          // Redirect to home or success page
          setTimeout(() => {
            router.push("/");
          }, 2000);
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to complete profile. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error("Error submitting consents & review:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to complete profile. Please try again.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Debug formik values
  useEffect(() => {
    console.log("Formik values changed:", formik.values);
  }, [formik.values]);

  if (isLoading) {
    return (
      <div>
        <div>Loading final review...</div>
      </div>
    );
  }

  return (
    <div className={css.consents}>
      <h2>Step 5: Consents, Availability & Final Review (School Bus Driver)</h2>
      <h5>Final step! Review your School Bus Driver profile, agree to consents, and specify your availability.</h5>

      <form onSubmit={formik.handleSubmit}>
        {/* Required Consents & Agreements */}
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <h3>Required Consents & Agreements</h3>
          <label htmlFor="" className={css.mb16}>(All checkboxes below are required to complete your profile *)</label>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Profile Sharing Consent: *</h6>
              <p>I consent to allow DriverJobz to share my profile information (excluding full sensitive numbers like SSN/License# until Hiring Packet approval) and indicate document upload status with registered employers/districts on this platform for employment consideration. I understand I control full document/detail release via Hiring Packet requests.</p>
            </div>
            {formik.touched.consentShareProfile && formik.errors.consentShareProfile && (
              <span className={css.error}>
                {formik.errors.consentShareProfile}
              </span>
            )}
          </div>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Background Check Consent: *</h6>
              <p>I understand that potential employers/districts will conduct background checks as a condition of hire. This includes DOT-required checks (PSP Report, Clearinghouse queries, MVR) as well as state/district specific checks which may include criminal background checks required for working with children. I consent to these checks being performed by districts/companies I connect with or apply to via DriverJobz.</p>
            </div>
            {formik.touched.consentBackgroundCheck && formik.errors.consentBackgroundCheck && (
              <span className={css.error}>
                {formik.errors.consentBackgroundCheck}
              </span>
            )}
          </div>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>FMCSA Clearinghouse Consent: *</h6>
              <p>As a CDL holder subject to FMCSA Drug & Alcohol Testing regulations, I provide specific consent for prospective and current employers/districts registered in the Clearinghouse to conduct limited queries of the FMCSA Drug & Alcohol Clearinghouse regarding my record. I understand I will be asked separately by the company/district to provide specific consent within the Clearinghouse portal for any required full queries.</p>
            </div>
            {formik.touched.consentClearinghouse && formik.errors.consentClearinghouse && (
              <span className={css.error}>
                {formik.errors.consentClearinghouse}
              </span>
            )}
          </div>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Certification of Truthfulness: *</h6>
              <p>I certify that all information provided in this application profile is true, accurate, and complete to the best of my knowledge. I understand that any misrepresentation, falsification, or omission of information may result in disqualification from employment opportunities or termination if hired.</p>
            </div>
            {formik.touched.consentCertifyInfo && formik.errors.consentCertifyInfo && (
              <span className={css.error}>
                {formik.errors.consentCertifyInfo}
              </span>
            )}
          </div>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Terms & Privacy Agreement: *</h6>
              <p>I acknowledge that I have read and agree to the DriverJobz&nbsp;<Link  href="/terms-and-conditions">Terms of Service</Link>&nbsp;and&nbsp;<Link href="/privacy-policy">Privacy Policy</Link>.</p>
            </div>
            {formik.touched.consentCertifyInfo && formik.errors.consentCertifyInfo && (
              <span className={css.error}>
                {formik.errors.consentCertifyInfo}
              </span>
            )}
          </div>
        </div>

        {/* Availability Information */}
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <h3>Availability Information</h3>
          <div className={css.labelDiv}>
            <label htmlFor="">Date Available for Work: <sup>*</sup></label>
          </div>
          <div className={css.col01}>
            <Dropdown
              options={availabilityOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
              value={formik.values.availability}
              placeholder="Select Availability"
              onChange={(value) => formik.setFieldValue("availability", value)}
              error={formik.touched.availability && formik.errors.availability ? formik.errors.availability : undefined}
              name="availability"
            />
          </div>
        </div>

          {/* Specific Date (conditional) */}
          {(() => {
            const selectedOption = availabilityOptions.find(opt => opt.formValueId.toString() === formik.values.availability);
            const showSpecificDate = selectedOption?.label.en.toLowerCase().includes("specific date");
            console.log("Availability option:", selectedOption?.label.en, "Show specific date:", showSpecificDate);

            return showSpecificDate && (
              <div>
                <label>
                  Specific Date: *
                </label>
                <DateInput
                  selected={formik.values.availabilitySpecificDate}
                  onChange={(date) => {
                    console.log("DateInput onChange called with:", date);
                    if (date !== null) {
                      formik.setFieldValue("availabilitySpecificDate", date);
                    } else {
                      console.log("Date is null, not updating field");
                    }
                  }}
                  placeholder="Select specific date"
                  minDate={new Date()} // Ensure future dates only
                />

                {/* Fallback: Regular HTML date input for debugging */}
                <div>
                  <label>Fallback date input (for debugging):</label>
                  <input
                    type="date"
                    value={formik.values.availabilitySpecificDate ? formik.values.availabilitySpecificDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => {
                      const dateValue = e.target.value ? new Date(e.target.value) : null;
                      console.log("Fallback date input changed:", dateValue);
                      formik.setFieldValue("availabilitySpecificDate", dateValue);
                    }}
                  />
                </div>
                {formik.touched.availabilitySpecificDate && formik.errors.availabilitySpecificDate && (
                  <div>
                    {formik.errors.availabilitySpecificDate}
                  </div>
                )}
              </div>
            );
          })()}

          {/* Preferred Employment Type */}
          <div className={`${css.formRow} ${css.dBlaco}`}>
            <label className={css.mb16}>Preferred Employment Type(s):&nbsp;<sup>*</sup>&nbsp;<span>(Check all that apply)</span></label>
            <>
              {employmentTypeOptions.map((option) => (
                <div key={option.formValueId} className={css.checkBox}>
                  <input
                    type="checkbox"
                    name="employmentType"
                    value={option.formValueId.toString()}
                    checked={formik.values.employmentType.includes(option.formValueId.toString())}
                    onChange={(e) => {
                      const value = e.target.value;
                      const currentValues = formik.values.employmentType;
                      if (e.target.checked) {
                        formik.setFieldValue("employmentType", [...currentValues, value]);
                      } else {
                        formik.setFieldValue("employmentType", currentValues.filter(v => v !== value));
                      }
                    }}
                  />
                  <span className={css.checkmark}></span>
                  <div className={css.text}>
                    <p>{option.label.en}</p>
                  </div>
                </div>
              ))}
            </>
            {formik.touched.employmentType && formik.errors.employmentType && (
              <span className={css.error}>
                {formik.errors.employmentType}
              </span>
            )}
          </div>

          {/* Preferred Route Type */}
          <div className={`${css.formRow} ${css.dBlaco}`}>
            <label className={css.mb16}>Preferred Route Type(s):&nbsp;<span>(Optional - Check all that apply)</span></label>
            <>
              {routeTypeOptions.map((option) => (
                <div key={option.formValueId} className={css.checkBox}>
                  <input
                    type="checkbox"
                    name="preferredRouteType"
                    value={option.formValueId.toString()}
                    checked={formik.values.preferredRouteType.includes(option.formValueId.toString())}
                    onChange={(e) => {
                      const value = e.target.value;
                      const currentValues = formik.values.preferredRouteType;
                      if (e.target.checked) {
                        formik.setFieldValue("preferredRouteType", [...currentValues, value]);
                      } else {
                        formik.setFieldValue("preferredRouteType", currentValues.filter(v => v !== value));
                      }
                    }}
                  />
                  <span className={css.checkmark}></span>
                  <div className={css.text}>
                    <p>{option.label.en}</p>
                  </div>
                </div>
              ))}
            </>
          </div>

          {/* Willing to Work Split Shift */}
          <div className={`${css.formRow} ${css.dBlaco}`}>
            <label className={css.mb16}>Willing to Work Split Shift?&nbsp;<sup>*</sup>&nbsp;<span>(Typical for school bus routes)</span></label>
            <ul className={css.radioList}>
              <li className={css.radioGroup}>
                <input
                  type="radio"
                  name="workSplitShift"
                  value="1"
                  checked={formik.values.workSplitShift === "1"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
              </li>
              <li className={css.radioGroup}>
                <input
                  type="radio"
                  name="workSplitShift"
                  value="0"
                  checked={formik.values.workSplitShift === "0"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
              </li>
              <li className={css.radioGroup}>
                <input
                  type="radio"
                  name="workSplitShift"
                  value="2"
                  checked={formik.values.workSplitShift === "2"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Prefer Not</p>
              </li>
            </ul>
            {formik.touched.workSplitShift && formik.errors.workSplitShift && (
              <div>
                {formik.errors.workSplitShift}
              </div>
            )}
          </div>
        

        {/* Final Review */}
        <div className={css.reviewDetails}>
          <h2>Final Review</h2>
          <div className={css.note}>
            <h6>Note</h6>
            <p>Please carefully review all the information you&apos;ve provided below. Use the &quot;Edit Section&quot; links to make corrections.</p>
          </div>

          <div className={css.flexBox}>
            {/* Stage 2: Essentials */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 2: Essentials</h3>
                <button onClick={() => router.push(`/${lang}/profile/driver?step=1`)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>
              <ul className={css.content}>
                <li>
                  <strong>Category:</strong>
                  <span>School Bus Driver</span>
                </li>
                <li>
                  <strong>License:</strong>
                  <span>{driverData?.driverLicenseClass || "CDL-B"}, {driverData?.driverLicenseState || "NY"}, Exp: {driverData?.driverLicenseExpiration ? new Date(driverData.driverLicenseExpiration).toLocaleDateString() : "MM/DD/YYYY"}</span>
                </li>
                <li>
                  <strong>Endorsements:</strong>
                  <span>{driverData?.driverLicenseEndorsements?.join(", ") || "P, S"} | Restrictions: {driverData?.driverLicenseRestrictions?.join(", ") || "B - Corrective Lenses"}</span>
                </li>
                <li>
                  <strong>Safety Summary:</strong>
                  <span>Accidents [{driverData?.numberOfAccidentsLast3Years || 0}], Violations [{driverData?.numberOfTrafficViolationsLast3Years || 0}], Suspension [{driverData?.isDriverLicenseSuspended ? "Yes" : "No"}]</span>
                </li>
                <li>
                  <strong>ID/License Upload:</strong>
                  <span>[✓]</span>
                </li>
              </ul>
            </div>

            {/* Stage 3 - Step 1: Experience */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Step 1: Experience</h3>
                <button onClick={() => handleEditStep(1)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>
              <ul className={css.content}>
                <li>
                  <strong>School Bus Exp:</strong>
                  <span>[{driverData?.totalVerifiableBusDriverExperience || 3} Years] | CDL Exp: [{driverData?.totalVerifiableCdlExperience || 3} Years]</span>
                </li>
                <li>
                  <strong>Vehicle Types:</strong>
                  <span>[{driverData?.studentTransportationVehicle?.length ? "Selected Vehicle Types" : "Conventional School Bus, Small Bus w/ Lift"}]</span>
                </li>
                <li>
                  <strong>Passengers:</strong>
                  <span>[{driverData?.ageGroupTransported?.length ? "Selected Age Groups" : "Elementary, Middle"}] | Special Needs: [{driverData?.specialNeedTransported?.length ? "Yes" : "No"}] | Routes: [{driverData?.routeTypes?.length ? "Selected Route Types" : "Regular, Field Trips"}]</span>
                </li>
              </ul>
            </div>

            {/* Stage 3 - Step 2: History */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Step 2: History</h3>
                <button onClick={() => handleEditStep(2)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>
              <ul className={css.content}>
                <li>
                  <strong>Periods Reported:</strong>
                  <span>[{driverData?.driverEmploymentHistory?.length || 2}] (Covering MM/YYYY - Present)</span>
                </li>
                <li>
                  (Brief summary showing District/Contractor employment)
                </li>
              </ul>
            </div>

            {/* Stage 3 - Step 3: Medical */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Step 3: Medical</h3>
                <button onClick={() => handleEditStep(3)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>
              <ul className={css.content}>
                <li>
                  <strong>DOT Med Status:</strong>
                  <span>[Current / Valid] | Expires: [{driverData?.dotExpirationDate ? new Date(driverData.dotExpirationDate).toLocaleDateString() : "MM/DD/YYYY"}]</span>
                </li>
                <li>
                  <strong>State Certs:</strong>
                  <span>[{driverData?.holdBusCertification ? "Yes - NY Article 19-A" : "No"}]</span>
                </li>
              </ul>
            </div>

            {/* Stage 3 - Step 4: Documents */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Step 4: Documents</h3>
                <button onClick={() => handleEditStep(4)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>
              <ul className={css.content}>
                <li>
                  <strong>Med Card:</strong>
                  <span>[✓] | State Cert: [✓] | CPR/First Aid: [✓]</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Submit Section */}
          <div className={css.completeProfile}>
            <h3>Submit Your Full School Bus Driver Profile</h3>
            <p>By clicking &quot;Complete Profile&quot;, you confirm your review, accuracy, and agreement to the consents. This enables 1-Click Apply and allows connected districts/companies to request your full hiring docket.</p>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          {canGoBack && (
            <button type="button" className={css.back} onClick={goToPreviousStep} disabled={isLoading}>
              <img src="/images/icons/arrow_back.svg"/>
              Back
            </button>
          )}
          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            disabled={isLoading}
            className={css.exit}
          >
            Save Draft and Exit
          </button>
          <button type="submit" className={css.continue} disabled={isLoading}>{isLoading ? "Completing..." : "Complete Full Profile & Activate Features"}</button>
        </div>
      </form>
    </div>
  );
};

export default ConsentsReview;
