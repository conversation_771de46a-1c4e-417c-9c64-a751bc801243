export const qualifyFormCDL = {
  cdlClass: 388,
  dotMedicalCard: true,
  endorsements: [],
  airbrakeCertRequired: true,
  twicCardRequired: "",
  passportRequired: "",
  experienceMonths: "",
  preferredEquipment: [],
  preferredRoutes: [],
  willingToTrain: "",
  trainingProgram: "",
  numberOfMovingViolations: {
    allowed: "",
    count: "",
    years: "",
  },
  numberOfAccidents: {
    allowed: "",
    count: "",
    years: "",
  },
  preventableAccidentsYears: {
    allowed: "",
    count: "",
    years: "",
  },
  seriousViolationsYears: {
    allowed: "",
    count: "",
    years: "",
  },
  duiYears: {
    allowed: "",
    count: "",
    years: "",
  },
  suspensionYears: {
    allowed: "",
    count: "",
    years: "",
  },
  drivingRecordOther: "",
  screeningChecks: [],
  physicalRequirements: [],
  physicalLiftingLimit: "",
  driverLanguages: [],
  minAge: "",
  isOtherRequirements: "",
  otherRequirements: []
};

export const qualifyBasicValues = {
  ...qualifyFormCDL,
  otherPhysicalRequirements: "",
  otherRequirementsText: "",
  physicalReq: "",
  drivingReq: ""
}

export const qualifyFormschool = {
  cdlClass: "",
  dotMedicalCard: true,
  endorsements: [392, 657],
  experienceMonths: "",
  schoolDrivingExperience: "",
  preferredEquipment: [],
  isCertificationRequired: "",
  certifications: [],
  isTrainingProgram: "",
  trainingPrograms: [],
  screeningChecks: [],
  pointsOnLicense: "",
  numberOfMovingViolations: {
    allowed: "",
    count: "",
    years: "",
  },
  numberOfAccidents: {
    allowed: "",
    count: "",
    years: "",
  },
  preventableAccidentsYears: {
    allowed: "",
    count: "",
    years: "",
  },
  seriousViolationsYears: {
    allowed: "",
    count: "",
    years: "",
  },
  duiYears: {
    allowed: "",
    count: "",
    years: "",
  },
  suspensionYears: {
    allowed: "",
    count: "",
    years: "",
  },
  physicalRequirements: [],
  physicalLiftingLimit: "",
  driverLanguages: [],
  minAge: "",
  isOtherRequirements: "",
  otherRequirements: []
};

export const qualifySchoolValues = {
  ...qualifyFormschool,
  certificationsText: "",
  otherRequirementsText: "",
  physicalReq: "",
  drivingReq: "",
}