"use client";

import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { paySchoolTypes, booleanFlags, bonusOfferArray, otherValue } from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import { PayProps, PaySchoolForm } from "../../../../types/jobpostingform";
import InputFieldRange from "@/components/Common/Form/InputFieldRange";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { paySchoolFormValues } from "@/initialValues/payStructureValues";
import { payFormPayload } from "@/submitHandler/payFormSubmit";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import { addPerPrefix, deepCleanValues, handleArrayFields, numericInput, safeFetch, scrollToFirstError } from "@/utils/utils";
import Textarea from "@/components/Common/Form/Textarea";

const PayStructure = ({ formFields, setCurrentStep, setLoading }: PayProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<PaySchoolForm>({
    initialValues: paySchoolFormValues,
    validationSchema: Yup.object({}),

    onSubmit: async(values: PaySchoolForm) => {
      setLoading(true);
      const payload = payFormPayload(values, isDraft, formFields);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 3, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });
  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost?.payStructure) {
        const cleanedJobPost = deepCleanValues(result?.jobPost?.payStructure);

        const arrayFields = handleArrayFields(cleanedJobPost, {
          benefits: { outputKey: "otherBenefit", addField: "other" },
          driverPerksAndPrograms: { }
        });

        const newObject = {} as Record<string, unknown>;

        const matchingBonuses = bonusOfferArray.filter(
          (key) => key in cleanedJobPost && cleanedJobPost[key] === true
        );

        if(cleanedJobPost.layoverPay) {
          matchingBonuses.push("layoverPay");
        }

        if(cleanedJobPost.otherBonusDescription) {
          matchingBonuses.push("other");
        }

        if(cleanedJobPost?.overtimeAfterHours) {
          newObject.overtimeOption = "other";
        }

        newObject.stopPayExcludeFirstLast = cleanedJobPost?.stopPayExcludeFirstLast
          ? ["stopPayExcludeFirstLast"]
          : [];

        if(cleanedJobPost.earningEstimate) {
          const estimate = cleanedJobPost?.earningEstimate?.split("-");
          const estimate1 = estimate?.[1]?.split("/");
          const period = addPerPrefix(estimate1?.[1]);
          const period1 = formFields?.["period-frequency-job-posting-cdl"].find(list => list.label === period)?.value;

          newObject.averageEarningsEstimate = estimate?.[0];
          newObject.averageEarningsEstimate1 = estimate1?.[0];
          newObject.estimatePeriod = Number(period1);
        }

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...newObject,
          bonusOffered: matchingBonuses
        });
      }
    };

    fetchData();
  }, [])
  
  console.log(formik.values, formFields)

  return (
    <div className={styles.payStructureInfo}>
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Compensation Structure</h2>
          <RadioField 
            label="Pay Structure"
            fieldName="type"
            formik={formik}
            radioArray={paySchoolTypes}
          />
          {formik.values.type === "HOURLY" && (
            <>
              <div className={styles.rowField}>
                <TextField
                  className="columnWidth_3"
                  label="Starting Hourly Rate"
                  fieldName="startingHourlyRate"
                  placeholder="e.g., $25/hour"
                  formik={formik}
                  handleChange={numericInput}
                />
                <InputFieldRange
                  className="columnWidth_3"
                  firstClass="columnWidthAdjust"
                  secondClass="columnWidthAdjust"
                  label="Hourly Rate Range"
                  fieldName="hourlyRateRangeMin"
                  fieldName1="hourlyRateRangeMax"
                  textName="To"
                  placeholder="e.g., $25/hour"
                  placeholder1="e.g., $40/hour"
                  formik={formik}
                  hide={true}
                  handleChange={numericInput}
                />
              </div>
              <RadioField
                label="Overtime Available / Rate"
                fieldName="overtimeOption"
                formik={formik}
                radioArray={[
                  ...(formFields?.["overtime-job-posting-cdl"] || []),
                  { label: "Other", value: "other" },
                ]}
                textClassName="radioInputWidth"
                textFieldName="overtimeAfterHours"
                textPlaceholder="8 hrs"
                handleChange={numericInput}
                textInput={true}
              />
            </>
          )}
          {formik.values.type === "PER_DAY" && (
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                label="Pay Per Day"
                fieldName="payPerDay"
                placeholder="e.g., $25/day"
                formik={formik}
                handleChange={numericInput}
              />
              <TextField
                className="columnWidth_3"
                label="Typical Hours per Day"
                fieldName="typicalHoursPerDay"
                placeholder="e.g., 10-12 hours"
                formik={formik}
                hide={true}
              />
            </div>
          )}
          {formik.values.type === "PER_WEEK" && (
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                label="Pay Per Route"
                fieldName="payPerWeek"
                placeholder="e.g., $25/week"
                formik={formik}
                handleChange={numericInput}
              />
              <TextField
                className="columnWidth_3"
                label="Specify Route Definition"
                fieldName="typicalHoursDaysWeek"
                placeholder="e.g., AM/PM combo, One-way trip"
                formik={formik}
                hide={true}
              />
            </div>
          )}
          {formik.values.type === "SALARY" && (
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                label="Annual Salary"
                fieldName="annualSalary"
                placeholder="e.g., $2500/year"
                formik={formik}
                handleChange={numericInput}
              />
              <InputFieldRange
                className="columnWidth_3"
                firstClass="columnWidthAdjust"
                secondClass="columnWidthAdjust"
                label="Salary Range"
                fieldName="salaryRangeMin"
                fieldName1="salaryRangeMax"
                textName="To"
                placeholder="e.g., $2500/year"
                placeholder1="e.g., $4000/year"
                formik={formik}
                hide={true}
                handleChange={numericInput}
              />
            </div>
          )}
          {formik.values.type === "COMBINATION_OTHER" && (
            <Textarea
              label="Combination / Other"
              fieldName="combinationDescription"
              placeholder="Describe structure (e.g., CPM + Hourly for detention, accessorial pay)"
              formik={formik}
            />
          )}
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Minimum Guaranteed Hours / Pay"
              fieldName="minimumGuaranteedPay"
              placeholder="Must be > 0"
              formik={formik}
              tooltipMsg="Specify the minimum hours or pay guaranteed per day/week, even if routes are short. This is important for driver income stability."
            />
            <RadioField
              className="radioFieldWidth_4"
              fieldName="minimumGuaranteedPayPeriod"
              formik={formik}
              radioArray={formFields?.["period-job-posting-cdl"]}
            />
          </div>
          <div className={styles.rowField}>
            <InputFieldRange
              className="columnWidth_3"
              firstClass="columnWidthAdjust"
              secondClass="columnWidthAdjust"
              label="Average Weekly Earnings Estimate"
              fieldName="averageEarningsEstimate"
              fieldName1="averageEarningsEstimate"
              textName="To"
              placeholder="e.g., $1,500"
              placeholder1="$1,800"
              formik={formik}
              hide={true}
            />
            {/* <RadioField
              className="radioWidthAdjust_3"
              fieldName="averageEarningsEstimate"
              formik={formik}
              radioArray={formFields?.["period-frequency-job-posting-cdl"]}
            /> */}
          </div>
          <RadioField
            label="Pay Schedule"
            fieldName="paySchedule"
            formik={formik}
            radioArray={formFields?.["pay-schedule-job-posting-cdl"]}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Additional Compensation</h2>
          <RadioField 
            label="Pay for Non-Driving Time"
            desc=" - Check all that apply"
            fieldName="nonDrivingTime"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.nonDrivingTime &&
            <>
              <CheckboxField 
                fieldName="nonDrivingPay"
                formik={formik}
                checkboxArray={[ 
                  ...(formFields?.["paid-duties-job-posting-school-bus-driver"] || []), 
                  otherValue
                ]}
                hide={true}
              />
              {formik.values.nonDrivingPay.includes("other") &&
                <div className={styles.rowField}>
                  <TextField
                    className="columnWidth_3"
                    fieldName="otherPayText"
                    placeholder="Enter here"
                    formik={formik}
                    hide={true}
                  />
                </div>
              }
            </>
          }
          <RadioField
            label="Sign-on Bonus Offered?"
            fieldName="signOnBonusOffered"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.signOnBonusOffered && (
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                label="Bonus Amount"
                fieldName="signOnBonusAmount"
                placeholder="Must be > 0"
                formik={formik}
                hide={true}
              />
              <TextField
                className="columnWidth_3"
                label="Payout Structure"
                fieldName="signOnBonusPayoutStructure"
                placeholder="e.g., $1000 after 30 days, $2000 after 90 days"
                formik={formik}
                hide={true}
              />
            </div>
          )}
          <RadioField 
            label="Other Pay / Bonuses Offered"
            desc=" - Check all that apply"
            fieldName="payBonusOffered"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.payBonusOffered &&
            <>
              <CheckboxField 
                fieldName="bonusOffered"
                formik={formik}
                checkboxArray={[
                  ...(formFields?.["types-of-bonusesdifferential-pay-job-posting-school-bus-driver"] || []),
                  otherValue
                ]}
                hide={true}
              />
              {formik.values.bonusOffered.includes("other") &&
                <div className={styles.rowField}>
                  <TextField
                    className="columnWidth_3"
                    fieldName="otherBonusDescription"
                    placeholder="Enter here"
                    formik={formik}
                    hide={true}
                  />
                </div>
              }
            </>
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Benefits <span>- Select benefits offered for this specific position/employment type</span></h2>
          <RadioField
            label="Benefits Package Includes?"
            fieldName="benefitsPackage"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.benefitsPackage && 
            <>
              <CheckboxField 
                label="Check all that apply"
                fieldName="benefits"
                formik={formik}
                checkboxArray={[ 
                  ...(formFields?.["benefits-package-includes-job-posting-cdl"] || []), 
                  otherValue
                ]}
                hide={true}
              />
              {formik.values.benefits.includes("other") &&
                <div className={styles.rowField}>
                  <TextField
                    className="columnWidth_3"
                    fieldName="otherBenefit"
                    placeholder="Enter here"
                    formik={formik}
                    hide={true}
                  />
                </div>
              }
            </>
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Orientation Information</h2>
          <RadioField
            label="Orientation Required?"
            fieldName="orientationRequired"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {/* <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Duration"
              fieldName="duration"
              placeholder="Must be > 0"
              formik={formik}
              hide={true}
            />
            <RadioField
              className="radioWidthAdjust"
              fieldName="timeUnit"
              formik={formik}
              radioArray={formFields?.["time-unit-job-posting-cdl"]}
              hide={true}
            />
            <TextField
              className="columnWidth_3"
              label="Location"
              fieldName="orientationLocation"
              placeholder="e.g., Online, Dallas TX Terminal"
              formik={formik}
              hide={true}
            />
          </div>
          <RadioField
            label="Paid Orientation / Training?"
            fieldName="paidOrientation"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          <CheckboxField 
            label="Expenses Covered"
            desc=" - Check all that apply"
            fieldName="expensesCovered"
            formik={formik}
            checkboxArray={formFields?.["expenses-covered-job-posting-cdl"]}
            hide={true}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Pay Rate"
              fieldName="payRate"
              placeholder="Must be > 0"
              formik={formik}
              hide={true}
            />
            <RadioField
              className="radioWidthAdjust"
              fieldName="payRate"
              formik={formik}
              radioArray={formFields?.["time-unit-job-posting-cdl"]}
              hide={true}
            />
          </div> */}
          {formik.values.orientationRequired && (
            <>
              <div className={styles.rowField}>
                <TextField
                  className="columnTinyWidth"
                  label="Duration"
                  fieldName="orientationDuration"
                  placeholder="Must be > 0"
                  formik={formik}
                  hide={true}
                  handleChange={numericInput}
                />
                <RadioField
                  className="radioWidthAdjust_2"
                  fieldName="orientationDurationUnit"
                  formik={formik}
                  radioArray={formFields?.["time-unit-job-posting-cdl"]}
                  hide={true}
                />
                <TextField
                  className="columnWidth_3"
                  label="Location"
                  fieldName="orientationLocation"
                  placeholder="e.g., Online, Dallas TX Terminal"
                  formik={formik}
                  hide={true}
                />
              </div>
              <RadioField
                label="Paid Orientation?"
                fieldName="paidOrientation"
                formik={formik}
                radioArray={booleanFlags}
                hide={true}
              />
              {formik.values.paidOrientation && (
                <div className={styles.rowField}>
                  <TextField
                    className="columnTinyWidth"
                    label="Pay Rate"
                    fieldName="orientationPayRate"
                    placeholder="Must be > 0"
                    formik={formik}
                    hide={true}
                    handleChange={numericInput}
                  />
                  <RadioField
                    className="radioWidthAdjust_2"
                    fieldName="orientationPayUnit"
                    formik={formik}
                    radioArray={formFields?.["time-unit-job-posting-cdl"]}
                    hide={true}
                  />
                </div>
              )}
              <CheckboxField
                label="Expenses Covered"
                desc=" - Check all that apply"
                fieldName="orientationExpense"
                formik={formik}
                checkboxArray={formFields?.["expenses-covered-job-posting-cdl"]}
                hide={true}
              />
            </>
          )}
          <ButtonComponent
            backBtnHandler={() => setCurrentStep(1)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm();
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default PayStructure;
