import CDL_BasicForm from "../CDL-Driver/BasicForm";
import CDL_PayStructure from "../CDL-Driver/PayStructure";
import CDL_TruckDetails from "../CDL-Driver/TruckDetails";
import CDL_Qualifications from "../CDL-Driver/Qualifications";
import CDL_JobDescription from "../CDL-Driver/JobDescription";

import School_BasicForm from "../SchoolBus-Driver/BasicForm";
import School_PayStructure from "../SchoolBus-Driver/PayStructure";
import School_TruckDetails from "../SchoolBus-Driver/TruckDetails";
import School_Qualifications from "../SchoolBus-Driver/Qualifications";
import School_JobDescription from "../SchoolBus-Driver/JobDescription";

export const jobTitle = {
  1: "CDL Driver",
  52: "School Bus Driver",
  3: "Bus Aide / Assistant"
}

export const stepHeading = {
  1: [
    {
      title: "Position Details",
      subCategory:
        "Enter the basic information about the driver position. Each posting represents one type of position and lasts 30 days.",
    },
    {
      title: "Compensation & Benefits",
      subCategory:
        "Detail the pay structure, potential earnings, and benefits offered for this position. Guaranteed minimum pay is required.",
    },
    {
      title: "Equipment & Freight",
      subCategory: "Describe the vehicle, equipment, and freight involved.",
    },
    {
      title: "Requirements & Qualifications",
      subCategory:
        "Specify the necessary license, experience, driving record, and other qualifications.",
    },
    {
      title: "Job Description & Application Process",
      subCategory:
        "Provide a compelling job description and outline the application steps.",
    },
  ],
  52: [
    {
      title: "Position & School Details",
      subCategory:
        "Enter the basic information about the School Bus Driver position. Each posting represents one type of position and lasts 30 days.",
    },
    {
      title: "Compensation & Benefits",
      subCategory:
        "Detail the pay structure, guaranteed hours/pay, and benefits for this School Bus Driver position.",
    },
    {
      title: "Bus Type Information",
      subCategory: "Describe the buses used, student population, and route characteristics.",
    },
    {
      title: "Requirements & Qualifications",
      subCategory:
        "Specify the license, endorsements, experience, certifications, and checks needed for this role.",
    },
    {
      title: "Job Description & Application Process",
      subCategory:
        "Provide a detailed description and outline the application steps. All applications must be submitted via DriverJobz.",
    },
  ],
};

export const dynamicComponentMap = {
  1: {
    BasicForm: CDL_BasicForm,
    PayStructure: CDL_PayStructure,
    TruckDetails: CDL_TruckDetails,
    Qualifications: CDL_Qualifications,
    JobDescription: CDL_JobDescription,
  },
  52: {
    BasicForm: School_BasicForm,
    PayStructure: School_PayStructure,
    TruckDetails: School_TruckDetails,
    Qualifications: School_Qualifications,
    JobDescription: School_JobDescription,
  },
};
