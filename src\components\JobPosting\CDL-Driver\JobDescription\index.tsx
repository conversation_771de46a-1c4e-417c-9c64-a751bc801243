"use client";

import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import {
  checkboxState,
  booleanFlags,
  postingVisibility,
} from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import {
  DescProps,
  DescriptionFormValues
} from "../../../../types/jobpostingform";
import Textarea from "@/components/Common/Form/Textarea";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import DatePickerComponent from "@/components/Common/Form/DatePickerComponent";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { deepCleanValues, getCommonKeys, numericInput, safeFetch, scrollToFirstError } from "@/utils/utils";
import { jobFormCDL } from "@/initialValues/jobDescriptionValues";
import { getJobFormSchema } from "@/schemas/jobDescriptionSchema";
import { jobFormPayload } from "@/submitHandler/jobDesciptionSubmit";

const JobDescription = ({
  formFields,
  companyDetails,
  setCurrentStep,
  setLoading
}: DescProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<DescriptionFormValues>({
    initialValues: jobFormCDL(companyDetails),
    validationSchema: getJobFormSchema(isDraft),

    onSubmit: async (values: DescriptionFormValues) => {
      setLoading(true);
      const payload = jobFormPayload(values, isDraft);

      await safeFetch(
        () => jobPostingUpdate(payload, jobId, () => {}, 0, isDraft, router),
        {},
        () => setLoading(false)
      );
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost) {
        const initialKeys = getCommonKeys(jobFormCDL(companyDetails), result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);

        const newObject = {} as Record<string, unknown>;

        newObject.eeoConfirmed = cleanedJobPost?.eeoConfirmed
          ? [1] : [];

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...newObject
        });
      }
    };

    fetchData();
  }, [])

  return (
    <div className={styles.payStructureInfo}>
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Job Description</h2>
          <Textarea
            label="Detailed Job Description & Responsibilities"
            fieldName="jobDescription"
            placeholder="Operate Class A tractor-trailer safely and efficiently... Haul refrigerated freight primarily in the Southeast region... Perform pre/post-trip inspections... Maintain HOS compliance using ELD... Communicate effectively with dispatch... Strong commitment to safety required... Join our growing tea"
            formik={formik}
            tooltipMsg="Describe the role, typical day, key duties, expectations, and why a driver should work for your company. Be clear and engaging."
          />
          <CheckboxField
            label="Key Responsibilities Summary"
            desc=" - Check to highlight"
            fieldName="keyResponsibilities"
            formik={formik}
            checkboxArray={formFields?.["key-responsibilities-job-posting-cdl"]}
            hide={true}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Application Process</h2>
          <Textarea
            label="Application Method"
            desc=" - Applications are submitted via DriverJobz"
            fieldName="applicationMethod"
            placeholder={`Applications for this position are accepted exclusively through the DriverJobz platform via the "1-Click Apply" button (available to fully registered drivers) or by sending a connection request (available to Stage 2+ drivers).`}
            formik={formik}
            hide={true}
          />
          <RadioField
            label="Required Documents for Application"
            desc=" (Initial Submission)"
            fieldName="applicationDocs"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
            tooltipMsg="The DriverJobz profile serves as the application. Only specify if anything *extra* is needed initially."
          />
          {formik.values.applicationDocs &&
            <Textarea
              label="Describe Here"
              fieldName="additionalNotes"
              placeholder="Please provide a clear and specific list of additional requirement for this position"
              formik={formik}
              hide={true}
            />
          }
          <CheckboxField
            label="Interview Process Steps"
            desc=" - Check all that apply"
            fieldName="interviewSteps"
            formik={formik}
            checkboxArray={
              formFields?.["interview-process-steps-job-posting-cdl"]
            }
            hide={true}
          />
          <RadioField
            label="Hiring Process Timeline Estimate"
            fieldName="hiringProcessTimeline"
            formik={formik}
            radioArray={
              formFields?.["hiring-process-timeline-estimate-job-posting-cdl"]
            }
            hide={true}
          />
          <DatePickerComponent
            className="columnWidth_3"
            label="Target Joining Date"
            desc=" (MM/DD/YYYY)"
            fieldName="joiningDate"
            minDate={new Date()}
            formik={formik}
            hide={true}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Posting Details</h2>
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Contact Person for Questions"
              fieldName="contactPersonName"
              placeholder="Enter Contact Person Name"
              formik={formik}
              hide={true}
              disabled={true}
              editIcon={true}
            />
            <TextField
              className="columnWidth_3"
              label="Contact Email"
              fieldName="contactPersonEmail"
              placeholder="Enter Contact Person Email"
              formik={formik}
              hide={true}
              disabled={true}
              editIcon={true}
            />
            <TextField
              className="columnWidth_3"
              label="Contact Phone"
              fieldName="contactPersonPhone"
              placeholder="Enter Contact Person Phone"
              formik={formik}
              hide={true}
              disabled={true}
              editIcon={true}
              handleChange={numericInput}
            />
          </div>
          <RadioField
            className="fullWidthAdjust"
            label="Job Posting Visibility:"
            fieldName="visibility"
            formik={formik}
            radioArray={postingVisibility}
            hide={true}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Posting Duration"
              fieldName="postingDurationDays"
              formik={formik}
              hide={true}
              disabled={true}
              tooltipMsg="Job posts are live for 30 days. Expired posts can be saved as drafts, edited, and relisted from your dashboard."
            />
          </div>
          <CheckboxField
            className="fullWidthAdjust"
            fieldName="eeoConfirmed"
            formik={formik}
            checkboxArray={checkboxState}
            hide={true}
          />
          <ButtonComponent
            btnText1="Publish Job Posting"
            backBtnHandler={() => setCurrentStep(4)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm();
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default JobDescription;
