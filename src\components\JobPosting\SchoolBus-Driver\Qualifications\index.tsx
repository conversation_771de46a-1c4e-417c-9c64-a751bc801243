"use client";

import React, { useState, useEffect } from "react";
import { getIn, useFormik } from "formik";
import { ageRequirement, booleanFlags, driverRecords, driverSelection, proficiencyOptions } from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import { QualificationSchoolForm, QualifyProps } from "../../../../types/jobpostingform";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { qualifySchoolValues } from "@/initialValues/qualificationFormValues";
import DropdownField from "@/components/Common/Form/DropdownField";
import { addAllowedFields, deepCleanValues, getCommonKeys, handleArrayFields, languageReq, safeFetch, scrollToFirstError } from "@/utils/utils";
import { qualifySchoolFormPayload } from "@/submitHandler/qualificationSubmit";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import { getQualifyFormSchema } from "@/schemas/qaulificationSchema";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";

const Qualifications = ({ formFields, setCurrentStep, languages, otherLanguages, setLoading }: QualifyProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const combinedLanguages = [...languages, ...otherLanguages];
  const proficiencyValues = proficiencyOptions.map((list) => list.value);
  const languageValues = Array.from(
    new Set(
      combinedLanguages
        .map((lang) => lang?.value)
        .filter((list): list is string | number => typeof list === "string" || typeof list === "number")
    )
  );

  const formik = useFormik<QualificationSchoolForm>({
    initialValues: qualifySchoolValues,
    validationSchema: getQualifyFormSchema(isDraft, proficiencyValues, languageValues),

    onSubmit: async(values: QualificationSchoolForm) => {
      setLoading(true);
      const payload = qualifySchoolFormPayload(values, isDraft);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 5, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});

      if(result?.jobPost) {
        const initialKeys = getCommonKeys(qualifySchoolValues, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);
        
        const arrayFields = handleArrayFields(cleanedJobPost, {
          physicalRequirements: { field1DependsOn: "physicalLiftingLimit", addField1: "radioBtn" },
          certifications: { outputKey: "certificationsText" },
          otherRequirements: { outputKey: "otherRequirementsText" },
          endorsements: { },
          preferredEquipment: { },
          trainingPrograms: { },
          screeningChecks: { }
        });

        const drivingRecordReq = addAllowedFields(cleanedJobPost);

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...drivingRecordReq
        });
      }
    };

    fetchData();
  }, [])

console.log(formik.values, formFields)

  return (
    <div className={styles.payStructureInfo}>
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>License & Endorsements</h2>
          <RadioField 
            label="Required CDL Class"
            fieldName="cdlClass"
            formik={formik}
            radioArray={formFields?.["cdl-requirement-job-posting-cdl"]}
          />
          <CheckboxField 
            label="Required Endorsements"
            desc=" - (Must include P & S)"
            fieldName="endorsements"
            formik={formik}
            checkboxArray={formFields?.["cdl-endorsements-job-posting-cdl"]}
            selectedDefault={[392, 657]}
          />
          {formik.values.endorsements.includes(659) && 
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="otherPhysicalRequirements"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <RadioField
            label="Valid DOT Medical Card Required?"
            fieldName="dotMedicalCard"
            formik={formik}
            radioArray={booleanFlags}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Experience Requirements</h2>
          <RadioField 
            label="Minimum Driving Experience"
            desc=" (Any Type)"
            fieldName="experienceMonths"
            formik={formik}
            radioArray={formFields?.["driver-experience-job-posting-school-bus-driver"]}
          />
          <RadioField 
            label="Minimum School Bus Driving Experience"
            fieldName="schoolDrivingExperience"
            formik={formik}
            radioArray={formFields?.["driver-experience-job-posting-school-bus-driver"]}
          />
          <CheckboxField 
            label="Experience with Specific Needs Preferred / Required"
            desc=" - Check all that apply"
            fieldName="preferredEquipment"
            formik={formik}
            checkboxArray={formFields?.["driver-experience-job-posting-school-bus-driver"]}
            hide={true}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Certifications & Training Requirements</h2>
          <RadioField
            label="Required Certifications"
            desc=" - Check all that apply"
            fieldName="isCertificationRequired"
            formik={formik}
            radioArray={booleanFlags}
          />
          {formik.values.isCertificationRequired &&
            <>
              <CheckboxField 
                fieldName="certifications"
                formik={formik}
                checkboxArray={formFields?.["certifications-and-training-job-posting-school-bus-driver"]}
                hide={true}
              />
              {formik.values.certifications.includes(773) &&
                <div className={styles.rowField}>
                  <TextField
                    className="columnWidth_3"
                    fieldName="certificationsText"
                    placeholder="Enter here"
                    formik={formik}
                    hide={true}
                  />
                </div>
              }
            </>
          }
          <RadioField
            label="Training Provided?"
            fieldName="isTrainingProgram"
            formik={formik}
            radioArray={booleanFlags}
          />
          {formik.values.isTrainingProgram &&
            <CheckboxField 
              fieldName="trainingPrograms"
              formik={formik}
              checkboxArray={formFields?.["training-offered-job-posting-school-bus-driver"]}
              hide={true}
            />
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Background Check & Screening Requirements</h2>
          <CheckboxField 
            label="Screening Required"
            desc=" - Check all that apply - Many are legally mandated"
            fieldName="screeningChecks"
            formik={formik}
            checkboxArray={formFields?.["pre-employment-screening-testing-job-posting-cdl"]}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Driving Record Requirements</h2>
          {driverRecords.map((list) => {
            const isRadioSelected = getIn(formik.values, list.radioFieldName) === true;

            return (
              <div className={styles.wrapperContainer} key={list.title}>
                <span className={styles.titleClass}>{list.title}</span>
                <RadioField
                  styleClass={styles.radioFormWidth}
                  fieldName={list.radioFieldName}
                  formik={formik}
                  radioArray={booleanFlags}
                  hide={true}
                />
                <div className={styles.dropdownClass}>
                  <DropdownField 
                    className="dropdownWidth"
                    fieldName={list.dropdownField}
                    defaultLabel={list.placeholder}
                    formik={formik}
                    dropdownArray={list.dropdownField === "numberOfMovingViolations.count" 
                      ? driverSelection.slice(1, 11) : driverSelection
                    }
                    disabled={!isRadioSelected}
                  />
                  <span className={styles.textSpan}>In</span>
                  <DropdownField 
                    className="dropdownWidth"
                    fieldName={list.dropdownField1}
                    defaultLabel={list.placeholder1}
                    formik={formik}
                    dropdownArray={driverSelection.slice(1, 11)}
                    disabled={!isRadioSelected}
                  />
                </div>
              </div>
            )
          })}
          {formik.touched.drivingReq && formik.errors.drivingReq && typeof formik.errors.drivingReq === "string" && (
            <div className="error_msg">
              {formik.errors.drivingReq}
            </div>
          )}
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Physical Requirements</h2>
          <CheckboxField 
            className="fullWidthAdjust"
            label="Specify Physical Requirements"
            desc=" - Check all that apply"
            fieldName="physicalRequirements"
            formik={formik}
            checkboxArray={[ 
              ...(formFields?.["physical-abilities-requirements-job-posting-cdl"] || []),
              { label: "Ability to lift / carry up to  - (Specify based on loading req.)",
                value: "radioBtn"
              }
            ]}
            radioBtn={true}
            radioClassName="checkboxRadioAlign"
            radioFieldName="physicalLiftingLimit"
            radioArray={formFields?.["ability-to-lift-carry-up-to-job-posting-cdl"]}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Language Requirements</h2>
          {languageReq(languages, otherLanguages).map((list, idx) => 
            <div key={list.key} className={styles.rowField}>
              {list.fields.map((item) => 
                <DropdownField 
                  key={item.field}
                  className="columnWidth_3"
                  label={item.title}
                  fieldName={`driverLanguages.${idx}.${item.field}`}
                  defaultLabel={item.defaultLabel}
                  formik={formik}
                  dropdownArray={item.options}
                  hide={true}
                />
              )}
            </div>
          )}
          {formik.touched.driverLanguages && typeof formik.errors.driverLanguages === "string" && (
            <div className="error_msg">{formik.errors.driverLanguages}</div>
          )}
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Additional Requirements</h2>
          <RadioField
            label="What is the minimum age requirement to apply?"
            fieldName="minAge"
            formik={formik}
            radioArray={ageRequirement}
          />
          <RadioField
            label="Other Requirements"
            desc=" - Check all that apply"
            fieldName="isOtherRequirements"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          <CheckboxField 
            className="fullWidthAdjust"
            fieldName="otherRequirements"
            formik={formik}
            checkboxArray={formFields?.["other-requirements-job-posting-cdl"]}
            hide={true}
          />
          {formik.values.otherRequirements.includes(680) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="otherRequirementsText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <ButtonComponent
            backBtnHandler={() => setCurrentStep(3)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm()
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default Qualifications;
