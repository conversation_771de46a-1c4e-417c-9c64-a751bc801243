import {
  getAllCategories,
} from "@/services/driverFormService";
import { safeFetch } from "@/utils/utils";
import JobCategory from "@/components/JobCategory";
import { CategoryItem } from "@/types/jobpostingform";

export default async function JobCategoryPage({
  params,
}: {
  params: Promise<{ lang: "en" | "es" }>;
}) {
  const { lang } = await params;

  const categories: { data?: CategoryItem[] } = await safeFetch(getAllCategories, { data: [] });

  const categoryArray = categories?.data?.map((list) => {
    return {
      label: lang === "en" ? list?.translations?.en?.name ?? "" : list?.translations?.es?.name ?? "",
      value: list?.translations?.en?.transportationCategoryId ?? "",
    };
  }) ?? [];

  // showing category which are needed for now - remove this later
  const shownCategory = categoryArray.filter(list => [1, 52].includes(Number(list.value)));

  return (
    <JobCategory categories={shownCategory} />
  );
}
