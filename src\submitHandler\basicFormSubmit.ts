import { basicFormValues } from "@/initialValues/basicFormValues";
import { CompanyDetails } from "@/types/jobpostingform";
import { mergeObjectsWithFallback } from "@/utils/utils";
import { getCookie } from "cookies-next";

const jobCategoryStr = getCookie("jobCategory");
const jobCategoryId = jobCategoryStr ? Number(jobCategoryStr) : 1;

export const basicFormPayload = (values: Record<string, any>, isDraft: boolean, companyDetails: CompanyDetails) => {
  const cloneObject = JSON.parse(JSON.stringify(values));

  if (cloneObject.jobTitle === "other" && cloneObject?.otherJob) {
    cloneObject.jobTitle = cloneObject?.otherJob;
  }

  if (cloneObject?.routeType?.length > 0 && cloneObject?.routeType?.includes("other") && cloneObject.specificRoute) {
    cloneObject.routeType.push(cloneObject.specificRoute);
  }

  if (cloneObject?.serviceArea?.length > 0 && cloneObject?.serviceArea?.includes("other") && cloneObject.specificState) {
    cloneObject.serviceArea.push(cloneObject.specificState);
  }

  const routeTypes = cloneObject?.routeType?.filter((item: any) => item !== "other")
    .map(String) || [];
  const serviceAreas = cloneObject?.serviceArea?.filter((item: any) => item !== "other")
    .map(String) || [];

  const jobPost: Record<string, unknown> = {
    jobCategoryId: jobCategoryId,
    companyId: companyDetails?.company?.companyId,
    jobTitle: cloneObject.jobTitle ? String(cloneObject.jobTitle) : null,
    employmentTypes: cloneObject.employmentTypes,
    locationCity: cloneObject.locationCity,
    locationZipCode: cloneObject.locationZipCode
      ? Number(cloneObject.locationZipCode)
      : null,
    routeType: routeTypes,
    serviceArea: serviceAreas,
    workScheduleDays: cloneObject.workScheduleDays,
    specificServiceArea: cloneObject.specificServiceArea,
    numberOfOpenings: cloneObject.numberOfOpenings
      ? Number(cloneObject.numberOfOpenings)
      : null,
    stateId: cloneObject.stateId,
    joinPeriod: cloneObject.joinPeriod,
    workScheduleTime: cloneObject.workScheduleTime || [],
    workScheduleShifts: cloneObject.workScheduleShifts,
    hasPickUpLocation: typeof cloneObject?.hasPickUpLocation === 'boolean' ? cloneObject?.hasPickUpLocation : null
  };

  if (cloneObject.homeTimeFrequency === "other") {
    jobPost.customHomeTimeFrequency = cloneObject.customHomeTimeFrequency;
  } else {
    jobPost.homeTimeFrequency = cloneObject.homeTimeFrequency;
  }

  if (cloneObject.hasPickUpLocation) {
    jobPost.firstPickUpLocation = cloneObject.firstPickUpLocation;
    jobPost.nationwide = cloneObject.nationwide;
    jobPost.statewide = cloneObject.statewide;
    jobPost.pickUpLocationDistance = cloneObject.pickUpLocationDistance
      ? Number(cloneObject.pickUpLocationDistance)
      : null;
  }

  if (cloneObject.joinPeriod === 76) {
    jobPost.targetStartDate = cloneObject.targetStartDate
      ? cloneObject.targetStartDate instanceof Date
        ? cloneObject.targetStartDate.toISOString()
        : cloneObject.targetStartDate
      : null;
  }

  const mergeAndNormalizeEmptyValues = mergeObjectsWithFallback(basicFormValues, jobPost);

  return {
    currentStep: 1,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: mergeAndNormalizeEmptyValues
  };
};
