import { CompanyDetails } from "@/types/jobpostingform";

export const jobFormCDL = (companyDetails: CompanyDetails) => {
  return {
    jobDescription: "",
    keyResponsibilities: [],
    applicationMethod: "",
    applicationDocs: "",
    additionalNotes: "",
    interviewSteps: [],
    hiringProcessTimeline: "",
    joiningDate: "",
    contactPersonName: companyDetails?.company?.name,
    contactPersonEmail: companyDetails?.company?.email,
    contactPersonPhone: companyDetails?.company?.phoneNumber,
    visibility: "Public",
    postingDurationDays: 30,
    eeoConfirmed: [],
  };
};

export const jobFormSchool = (companyDetails: CompanyDetails) => {
  return {
    ...jobFormCDL(companyDetails),
    workingConditions: [],
    routeEnvironments: [],
    routeType: [],
    firstPickUp: "",
    lastDropOffLocation: "",
    totalRouteMileage: ""
  };
}
