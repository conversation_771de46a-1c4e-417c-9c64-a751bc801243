.columnField {
    display: flex;
    flex-direction: column;

    &.columnWidth_3 {
        width: calc((100% - 48px) / 3);
    }

    label {
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 4px;

        .important {
            color: #F91313;
        }
    }

    .dropdown {
        position: relative;

        .dropdownToggle {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
        }

        .iconWrapper {
            display: flex;
            margin-left: auto;
            transition: transform 0.3s ease;
            }

        .rotate {
            transform: rotate(180deg);
        }

        .dropdownMenu {
            background-color: #FFFFFF;
            border-radius: 4px;
            -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            max-height: 180px;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
            width: 100%;
            left: 0px;
            top: calc(100% + 8px);
            z-index: 1;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: #F5F5F5;
        }

        &::-webkit-scrollbar-thumb {
            background: #929292;
            border-radius: 24px;
        }
    }

    .dropdownToggle {
        background-color: #FFFFFF;
        border: 1px solid #707070;
        border-radius: 4px;
        width: 100%;
        height: 44px;
        color: #000000;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        text-align: left;
        padding: 10px 8px;
        outline: none !important;

        &:disabled {
            background-color: #F7F7F7;
        }

        &::placeholder {
            color: #707070;
            font-weight: 400;
            opacity: 1;
        }
    }

    .dropdownItem {
        background-color: #FFFFFF;
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 15px;
        line-height: 22px;
        height: 44px;
        padding: 4px 12px;
        width: 100%;
        cursor: pointer;
        text-align: left;
    }
}

.dropdownWidth {
    width: 30%;
    @media (max-width : 1300px) {
        min-width: 120px;
    }
}