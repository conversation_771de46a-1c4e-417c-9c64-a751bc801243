import { truckBasicValues } from "@/initialValues/truckFormValues";
import { VehicleChoolValues, VehicleFormValues } from "@/types/jobpostingform";
import { mergeObjectsWithFallback } from "@/utils/utils";

export const truckFormPayload = (
  values: VehicleFormValues,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(JSON.stringify(values)) as VehicleFormValues;

  if (cloneObject.vehicleType.includes(301) && cloneObject?.otherVehicleText) {
    cloneObject.vehicleType.push(cloneObject.otherVehicleText);
  }

  if (
    cloneObject.truckAmenities.includes("other") &&
    cloneObject?.truckAmenitiesText
  ) {
    cloneObject.truckAmenities.push(cloneObject.truckAmenitiesText);
  }

  if (cloneObject.inCabTech.includes(328) && cloneObject?.inCabTechText) {
    cloneObject.inCabTech.push(cloneObject.inCabTechText);
  }

  if (cloneObject.eldSystems.includes(336) && cloneObject?.eldSystemsText) {
    cloneObject.eldSystems.push(cloneObject.eldSystemsText);
  }

  if (
    cloneObject.freightTypes.includes("other") &&
    cloneObject?.freightTypesText
  ) {
    cloneObject.freightTypes.push(cloneObject.freightTypesText);
  }

  const truckType = cloneObject.vehicleType
    .filter((item) => item !== 301)
    .map(String);
  const amenities = cloneObject.truckAmenities
    .filter((item) => item !== "other")
    .map(String);
  const cabTech = cloneObject.inCabTech
    .filter((item) => item !== 328)
    .map(String);
  const eldSystem = cloneObject.eldSystems
    .filter((item) => item !== 336)
    .map(String);
  const freightType = cloneObject.freightTypes
    .filter((item) => item !== "other")
    .map(String);

  const jobPost: Record<string, unknown> = {
    vehicleType: truckType,
    vehicleTypeText: cloneObject.vehicleTypeText,
    truckAge: String(cloneObject?.truckAge),
    transmission: String(cloneObject?.transmission),
    truckAssignment: String(cloneObject?.truckAssignment),
    governedSpeed: cloneObject.governedSpeed,
    truckAmenities: amenities,
    inCabTech: cabTech,
    eldSystems: eldSystem,
    trailerTypes: cloneObject?.trailerTypes?.map(String),
    trailerLength: cloneObject.trailerLength,
    tankerContents: cloneObject.tankerContents,
    trailerAge: String(cloneObject?.trailerAge),
    freightTypes: freightType,
    loadingReqs: cloneObject?.loadingReqs?.map(String),
    dropHookPercent: String(cloneObject?.dropHookPercent),
    avgLoadWeight: cloneObject.avgLoadWeight,
  };

  if (cloneObject.trailerTypes.includes(355) && cloneObject?.otherTrailer) {
    jobPost.otherTrailer = cloneObject.otherTrailer;
  }

  const mergeAndNormalizeEmptyValues = mergeObjectsWithFallback(truckBasicValues, jobPost);
  
  return {
    currentStep: 3,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: mergeAndNormalizeEmptyValues
  };
};

export const truckSchoolFormPayload = (
  values: VehicleChoolValues,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(JSON.stringify(values)) as VehicleChoolValues;

  if (cloneObject.vehicleType.includes(723) && cloneObject?.otherVehicleText) {
    cloneObject.vehicleType.push(cloneObject.otherVehicleText);
  }

  if (
    cloneObject.vehicleFeatures.includes(741) &&
    cloneObject?.vehicleFeaturesText
  ) {
    cloneObject.vehicleFeatures.push(cloneObject.vehicleFeaturesText);
  }

  if (cloneObject.specialEquipmentDriverOperate.includes("other") && cloneObject?.specialEquipmentText) {
    cloneObject.specialEquipmentDriverOperate.push(cloneObject.specialEquipmentText);
  }

  const truckType = cloneObject.vehicleType
    .filter((item) => item !== 723)
    .map(String);
  const vehicleFeature = cloneObject.vehicleFeatures
    .filter((item) => item !== 741)
    .map(String);
  const specialEquipment = cloneObject.specialEquipmentDriverOperate
    .filter((item) => item !== "other")
    .map(String);

  const jobPost: Record<string, unknown> = {
    vehicleType: truckType,
    numberOfPassengers: cloneObject.numberOfPassengers ? Number(cloneObject.numberOfPassengers) : null,
    vehicleFeatures: vehicleFeature,
    specialEquipmentDriverOperate: specialEquipment,
    studentAgeGroup: cloneObject?.studentAgeGroup?.map(String),
    experienceWithSpecialNeedsStudents: cloneObject.experienceWithSpecialNeedsStudents,
    needsSupported: cloneObject?.needsSupported?.map(String),
    typicalRoute: cloneObject?.typicalRoute?.map(String),
    routeInfo: cloneObject?.routeInfo
  };

  return {
    currentStep: 3,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost
  };
};
