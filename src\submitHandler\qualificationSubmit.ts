import { qualifyFormCDL } from "@/initialValues/qualificationFormValues";
import { QualificationForm, QualificationSchoolForm } from "@/types/jobpostingform";
import { mergeObjectsWithFallback, removeAllowedFields } from "@/utils/utils";

export const qualifyFormPayload = (
  values: QualificationForm,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(JSON.stringify(values)) as QualificationForm;

  const jobPost: Record<string, unknown> = {
    endorsements: cloneObject?.endorsements?.map(String),
    preferredEquipment: cloneObject?.preferredEquipment?.map(String),
    preferredRoutes: cloneObject?.preferredRoutes?.map(String),
    screeningChecks: cloneObject?.screeningChecks?.map(String),
    dotMedicalCard: typeof cloneObject?.dotMedicalCard === 'boolean' ? cloneObject?.dotMedicalCard : null,
    airbrakeCertRequired: typeof cloneObject?.airbrakeCertRequired === 'boolean' ? cloneObject?.airbrakeCertRequired : null,
    twicCardRequired: typeof cloneObject?.twicCardRequired === 'boolean' ? cloneObject?.twicCardRequired : null,
    passportRequired: typeof cloneObject?.passportRequired === 'boolean' ? cloneObject?.passportRequired : null,
    willingToTrain: typeof cloneObject?.willingToTrain === 'boolean' ? cloneObject?.willingToTrain : null,
    isOtherRequirements: typeof cloneObject?.isOtherRequirements === 'boolean' ? cloneObject?.isOtherRequirements : null,
    driverLanguages: cloneObject.driverLanguages,
    drivingRecordOther: cloneObject?.drivingRecordOther,
    minAge: cloneObject.minAge ? Number(cloneObject.minAge) : null,
    experienceMonths: cloneObject.experienceMonths ? Number(cloneObject.experienceMonths) : null,
    cdlClass: cloneObject.cdlClass ? Number(cloneObject.cdlClass) : null,
  };

  delete cloneObject.physicalReq;
  delete cloneObject.drivingReq;

  if (cloneObject.isOtherRequirements) {
    if (
      cloneObject?.otherRequirements.includes("other") &&
      cloneObject.otherRequirementsText
    ) {
      cloneObject.otherRequirements.push(cloneObject?.otherRequirementsText);
    }
    jobPost.otherRequirements = cloneObject.otherRequirements
      .filter((item) => item !== "other")
      .map(String);
  }

  if (cloneObject.willingToTrain) {
    if (cloneObject?.trainingProgram)
      jobPost.trainingProgram = cloneObject?.trainingProgram;
  }

  if (
    cloneObject?.physicalRequirements.includes("other") &&
    cloneObject.otherPhysicalRequirements
  ) {
    cloneObject.physicalRequirements.push(
      cloneObject?.otherPhysicalRequirements
    );
  }

  if (
    cloneObject.physicalRequirements.includes("radioBtn") &&
    cloneObject.physicalLiftingLimit
  ) {
    jobPost.physicalLiftingLimit = cloneObject.physicalLiftingLimit;
  }

  jobPost.physicalRequirements = cloneObject.physicalRequirements
    .filter((item) => item !== "other" && item !== "radioBtn")
    .map(String);

  const drivingRecordPayload = removeAllowedFields(cloneObject);

  const mergeAndNormalizeEmptyValues = mergeObjectsWithFallback(qualifyFormCDL, jobPost);
  
  return {
    currentStep: 4,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
      ...mergeAndNormalizeEmptyValues,
      ...drivingRecordPayload
    }
  };
};

export const qualifySchoolFormPayload = (
  values: QualificationSchoolForm,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(JSON.stringify(values)) as QualificationSchoolForm;

  const jobPost: Record<string, unknown> = {
    cdlClass: cloneObject.cdlClass ? Number(cloneObject.cdlClass) : null,
    dotMedicalCard: typeof cloneObject?.dotMedicalCard === 'boolean' ? cloneObject?.dotMedicalCard : null,
    endorsements: cloneObject?.endorsements?.map(String),
    experienceMonths: cloneObject.experienceMonths ? Number(cloneObject.experienceMonths) : null,
    preferredEquipment: cloneObject?.preferredEquipment?.map(String),
    isCertificationRequired: typeof cloneObject?.isCertificationRequired === 'boolean' ? cloneObject?.isCertificationRequired : null,
    isTrainingProgram: typeof cloneObject?.isTrainingProgram === 'boolean' ? cloneObject?.isTrainingProgram : null,
    screeningChecks: cloneObject?.screeningChecks?.map(String),
    pointsOnLicense: cloneObject.pointsOnLicense ? Number(cloneObject.pointsOnLicense) : null,
    driverLanguages: cloneObject.driverLanguages,
    minAge: cloneObject.minAge ? Number(cloneObject.minAge) : null,
    isOtherRequirements: typeof cloneObject?.isOtherRequirements === 'boolean' ? cloneObject?.isOtherRequirements : null,
  };

  delete cloneObject.physicalReq;
  delete cloneObject.drivingReq;

  if (cloneObject.isCertificationRequired) {
    if (
      cloneObject?.certifications.includes(773) &&
      cloneObject.certificationsText
    ) {
      cloneObject.certifications.push(cloneObject?.certificationsText);
    }
    jobPost.certifications = cloneObject.otherRequirements.map(String);
  }

  if (cloneObject.isTrainingProgram) {
    jobPost.trainingPrograms = cloneObject.trainingPrograms.map(String);
  }

  if (
    cloneObject.physicalRequirements.includes("radioBtn") &&
    cloneObject.physicalLiftingLimit
  ) {
    jobPost.physicalLiftingLimit = cloneObject.physicalLiftingLimit;
  }

  jobPost.physicalRequirements = cloneObject.physicalRequirements
    .filter((item) => item !== "radioBtn")
    .map(String);

  if (cloneObject.isOtherRequirements) {
    if (
      cloneObject?.otherRequirements.includes(680) &&
      cloneObject.otherRequirementsText
    ) {
      cloneObject.otherRequirements.push(cloneObject?.otherRequirementsText);
    }
    jobPost.otherRequirements = cloneObject.otherRequirements.map(String);
  }

  const drivingRecordPayload = removeAllowedFields(cloneObject);

  return {
    currentStep: 4,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
        ...jobPost,
        ...drivingRecordPayload,
    },
  };
};
