@import "../../../../styles/global.scss";

.radioWrapper {
    display: flex;
    flex-direction: column;
    .labelRadio {
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;

        .desc {
            font-weight: 400;
        }

        .important {
            color: $red;
        }
    }

    .radioList {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        list-style: none;
        padding: 0;

        li {
            display: flex;
            column-gap: 11px;
            width: calc((100% - 96px) / 5);
            font-size: 14px;
            color: $gray-secondary;
            margin-bottom: 16px;
            position: relative;

            .radioContainer {
                user-select: none;
                padding-left: 30px;
            }

            input {
                position: absolute;
                opacity: 0;
                height: 0;
                width: 0;
            }

            .customRadio {
                position: absolute;
                left: 0;
                top: 0;
                height: 20PX;
                width: 20PX;
                border: 2px solid $gray-secondary;
                border-radius: 50%;
                cursor: pointer;

                &::after {
                    content: "";
                    position: absolute;
                    display: none;
                    top: 4px;
                    left: 4px;
                    width: 9px;
                    height: 9px;
                    border-radius: 50%;
                    background-color: $gray-secondary;
                }
            }

            input:checked~.customRadio {
                &::after {
                    display: block;
                }
            }
        }
    }
}

.radioContainer {
    align-items: center;
}

.radioWidthAdjust li {
    width: calc((100% - 24px) / 2) !important;
}

.radioWidthAdjust_2 {
    width: 17%;
    margin-top: 36px;
    li {
        width: calc((100% - 24px) / 2) !important;
    }
}

.radioWidthAdjust_3 {
    width: 33%;
    margin-top: 36px;

    li {
        width: calc((100% - 48px) / 3) !important;
    }
}

.fullWidthAdjust li {
    width: 100% !important;
}

.fullWidthLi {
    width: 45% !important;
}

.otherLabel {
    display: flex;
    div {
        margin: -13px 16px 0px;
    }
    input {
        opacity: 1 !important;
        position: static !important;
        height: auto !important;
        width: auto !important;
    }
}

.radioWidthChange {
    width: 33%;

    li {
        width: calc((100% - 48px) / 3) !important;
    }
}

.checkboxRadioAlign {
    margin: 16px 0px 0px !important;
    li {
        width: calc((100% - 96px) / 5) !important;
    }
}

.radioFieldWidth_4 {
    width: 60%;
    margin-top: 36px;

    li {
        width: calc((100% - 72px) / 4) !important;
    }
}