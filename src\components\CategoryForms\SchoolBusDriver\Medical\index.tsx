"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchSchoolBusDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  // FetchDriverDetailsResponse
} from "@/services/driverFormService";

import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import css from './schoolBusDriverMedical.module.scss';

interface driverBusCertifications {
  id: string;
  
busIssuingBody:string;
  certificationName: string;
  dateIssued: Date | null;
  expirationDate: Date | null;
}

interface OtherCertification {
  id: string;
  certificationName: string;
  issuingBody: string;
  dateIssued: Date | null;
  expirationDate: Date | null;
}

interface MedicalFormValues {
  dotMedicalCardStatus: string;
  dotExpirationDate: Date | null;
  dotExaminerName: string;
  dotExaminerPhone: string;
  dotNationalRegistryNumber: string;
  dotRestriction: string;
  dotExemption: string;
  hasMedicalVariance: boolean;
  // medicalVarianceDetails: string;
  holdOtherCertification: boolean;
  stateCertifications: driverBusCertifications[];
  otherCertifications: OtherCertification[];
}

// function isCertificationErrorArray(
//   value: unknown
// ): value is Array<FormikErrors<StateCertification | OtherCertification>> {
//   return Array.isArray(value);
// }

const Medical: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } =
    useSchoolBusDriverCategory();
  const router = useRouter();
  const [medicalStatusOptions, setMedicalStatusOptions] = useState<FormValue[]>(
    []
  );
  // const [states, setStates] = useState<State[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [formFields, detailsResponse] = await Promise.all([
          fetchSchoolBusDriverFormFields(),
         
          fetchDriverDetails(),
        ]);

        // Map API data to state and filter out empty options
        const medicalStatuses = (
          formFields["dot-medical-card-status-driver-cdl"] || []
        ).filter((option) => option.label?.en && option.label.en.trim() !== "");

        setMedicalStatusOptions(medicalStatuses);
        // setStates(stateList);

        // Pre-populate with existing data if available
        const driver = detailsResponse?.data?.driver;
        if (driver) {
          // const cleanedValues: MedicalFormValues = {
          //   dotMedicalCardStatus: driver.dotMedicalCardStatus?.toString() || "",
          //   dotExpirationDate: driver.dotExpirationDate
          //     ? new Date(driver.dotExpirationDate)
          //     : null,
          //   dotExaminerName: driver.dotExaminerName || "",
          //   dotExaminerPhone: driver.dotExaminerPhone || "",
          //   dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
          //   dotRestriction: driver.dotRestriction || "",
          //   dotExemption: driver.dotExemption || "",
          //   hasMedicalVariance: driver.holdBusCertification || false,
          //   // medicalVarianceDetails: driver.medicalVarianceDetails || "",
          //   holdOtherCertification: driver.holdOtherCertification || false,
          //   // stateCertifications: [],
  
          //   otherCertifications: (driver.driverOtherCertifications || []).map(
          //     (cert, index) => ({
          //       id: `cert-${index}-${Date.now()}`,
          //       certificationName: cert.certificateName || "",
          //       issuingBody: cert.issuingBody || "",
          //       dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
          //       expirationDate: cert.expirationDate
          //         ? new Date(cert.expirationDate)
          //         : null,
          //     })
          //   ),
          //   stateCertifications: (driver.driverBusCertifications || []).map(
          //     (cert, index) => ({
          //       id: `bus-cert-${index}-${Date.now()}`,
          //       certificationName: cert.certificateName || "",
          //       busIssuingBody: cert.issuingBody || "", // assuming state is stored in `issuingBody`
          //       dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
          //       expirationDate: cert.expirationDate
          //         ? new Date(cert.expirationDate)
          //         : null,
             
          //     })
          //   ),
          // };
          const cleanedValues: MedicalFormValues = {
  dotMedicalCardStatus: driver.dotMedicalCardStatus?.toString() || "",
  dotExpirationDate: driver.dotExpirationDate
    ? new Date(driver.dotExpirationDate)
    : null,
  dotExaminerName: driver.dotExaminerName || "",
  dotExaminerPhone: driver.dotExaminerPhone || "",
  dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
  dotRestriction: driver.dotRestriction || "",
  dotExemption: driver.dotExemption || "",
  hasMedicalVariance: driver.holdBusCertification || false,
  holdOtherCertification: driver.holdOtherCertification || false,

  otherCertifications:
    driver.holdOtherCertification && Array.isArray(driver.driverOtherCertifications)
      ? driver.driverOtherCertifications.map((cert, index) => ({
          id: `cert-${index}-${Date.now()}`,
          certificationName: cert.certificateName || "",
          issuingBody: cert.issuingBody || "",
          dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
          expirationDate: cert.expirationDate
            ? new Date(cert.expirationDate)
            : null,
        }))
      : [],

  stateCertifications:
    driver.holdBusCertification && Array.isArray(driver.driverBusCertifications)
      ? driver.driverBusCertifications.map((cert, index) => ({
          id: `bus-cert-${index}-${Date.now()}`,
          certificationName: cert.certificateName || "",
          busIssuingBody: cert.issuingBody || "",
          dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
          expirationDate: cert.expirationDate
            ? new Date(cert.expirationDate)
            : null,
        }))
      : [],
};

          formik.setValues(cleanedValues);
        }
      } catch (err) {
        console.error("Failed to fetch data:", err);
        toast.error("Failed to load form data. Please refresh the page.");
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, []);

  const createEmptyStateCertification = (): driverBusCertifications => ({
    id: `state-cert-${Date.now()}-${Math.random()}`,
    busIssuingBody: "",
    certificationName: "",
    dateIssued: null,
    expirationDate: null,
  });

  const createEmptyOtherCertification = (): OtherCertification => ({
    id: `other-cert-${Date.now()}-${Math.random()}`,
    certificationName: "",
    issuingBody: "",
    dateIssued: null,
    expirationDate: null,
  });

  const initialValues: MedicalFormValues = {
    dotMedicalCardStatus: "",
    dotExpirationDate: null,
    dotExaminerName: "",
    dotExaminerPhone: "",
    dotNationalRegistryNumber: "",
    dotRestriction: "",
    dotExemption: "",
    hasMedicalVariance: false,
    // medicalVarianceDetails: "",
    holdOtherCertification: false,
    stateCertifications: [],
    otherCertifications: [],
  };

  // Helper function to check if medical status requires expiration date
  const requiresExpirationDate = (statusId: string): boolean => {
    const option = medicalStatusOptions.find(
      (opt) => opt.formValueId.toString() === statusId
    );
    const label = option?.label.en.toLowerCase() || "";
    return (
      label.includes("current") ||
      label.includes("valid") ||
      label.includes("variance") ||
      label.includes("exemption")
    );
  };

  // Helper function to check if medical status is variance/exemption
  const isVarianceExemption = (statusId: string): boolean => {
    const option = medicalStatusOptions.find(
      (opt) => opt.formValueId.toString() === statusId
    );
    const label = option?.label.en.toLowerCase() || "";
    return label.includes("variance") || label.includes("exemption");
  };

  const createValidationSchema = () =>
    Yup.object().shape({
      dotMedicalCardStatus: Yup.string().required(
        "DOT Medical Card Status is required"
      ),
      dotExpirationDate: Yup.date()
        .nullable()
        .when("dotMedicalCardStatus", {
          is: (val: string) => requiresExpirationDate(val),
          then: (schema) =>
            schema.required("Expiration date is required for this status"),
          otherwise: (schema) => schema.notRequired(),
        }),
      dotExemption: Yup.string().when("dotMedicalCardStatus", {
        is: (val: string) => isVarianceExemption(val),
        then: (schema) =>
          schema.required("Medical variance/exemption details are required"),
        otherwise: (schema) => schema.notRequired(),
      }),
   stateCertifications: Yup.array().of(
    Yup.object().shape({
      certificationName: Yup.string().when("$hasMedicalVariance", {
        is: true,
        then: (schema) => schema.required("Certification name is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
      expirationDate: Yup.date().when("$hasMedicalVariance", {
        is: true,
        then: (schema) =>
          schema.nullable().required("Expiration date is required"),
        otherwise: (schema) => schema.nullable(),
      }),
      busIssuingBody: Yup.string().notRequired(),
      dateIssued: Yup.date().nullable(),
    })
  ),
    
   otherCertifications: Yup.array().of(
    Yup.object().shape({
      certificationName: Yup.string().when("$holdOtherCertification", {
        is: true,
        then: (schema) => schema.required("Certification name is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
      expirationDate: Yup.date().when("$holdOtherCertification", {
        is: true,
        then: (schema) =>
          schema.nullable().required("Expiration date is required"),
        otherwise: (schema) => schema.nullable(),
      }),
      issuingBody: Yup.string().notRequired(),
      dateIssued: Yup.date().nullable(),
    })
  ),
    });

  const handleSubmit = async (
    values: MedicalFormValues,
    shouldContinue: boolean = true
  ) => {
    console.log("Medical form submission started with values:", values);

    setIsLoading(true);
    try {
      const payload = {
  currentStage: 3,
  currentStep: 3,
  driver: {
    dotMedicalCardStatus: values.dotMedicalCardStatus
      ? parseInt(values.dotMedicalCardStatus)
      : undefined,
    dotExpirationDate: values.dotExpirationDate
      ? values.dotExpirationDate.toISOString()
      : null,
    dotExaminerName: values.dotExaminerName || "",
    dotExaminerPhone: values.dotExaminerPhone || "",
    dotNationalRegistryNumber: values.dotNationalRegistryNumber || "",
    dotRestriction: values.dotRestriction || "",
    dotExemption: values.dotExemption || "",
    holdBusCertification: values.hasMedicalVariance,
    holdOtherCertification: values.holdOtherCertification,
 
    ...(values.holdOtherCertification && {
      driverOtherCertifications: values.otherCertifications.map((cert, index) => ({
        certificateName: cert.certificationName,
        issuingBody: cert.issuingBody,
        dateIssued: cert.dateIssued ? cert.dateIssued.toISOString() : null,
        expirationDate: cert.expirationDate
          ? cert.expirationDate.toISOString()
          : null,
        rank: index + 1,
      })),
    }),

     
    ...(values.hasMedicalVariance && {
      driverBusCertifications: values.stateCertifications.map((cert, index) => ({
        certificateName: cert.certificationName,
        issuingBody: cert.busIssuingBody,
        dateIssued: cert.dateIssued ? cert.dateIssued.toISOString() : null,
        expirationDate: cert.expirationDate
          ? cert.expirationDate.toISOString()
          : null,
        rank: index + 1,
      })),
    }),
  },
};

    

      console.log(
        "Submitting medical payload:",
        JSON.stringify(payload, null, 2)
      );
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Medical information saved successfully!");
          window.scrollTo({ top: 0, behavior: "smooth" });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting medical information:", error);
      toast.error("Failed to save medical information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<MedicalFormValues>({
    initialValues,
    validationSchema: createValidationSchema(),
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const addStateCertification = () => {
    const newCert = createEmptyStateCertification();
    formik.setFieldValue("stateCertifications", [
      ...formik.values.stateCertifications,
      newCert,
    ]);
  };

  const removeStateCertification = (index: number) => {
    const updated = [...formik.values.stateCertifications];
    updated.splice(index, 1);
    formik.setFieldValue("stateCertifications", updated);
  };

  const addOtherCertification = () => {
    const newCert = createEmptyOtherCertification();
    formik.setFieldValue("otherCertifications", [
      ...formik.values.otherCertifications,
      newCert,
    ]);
  };

  const removeOtherCertification = (index: number) => {
    const updated = [...formik.values.otherCertifications];
    updated.splice(index, 1);
    formik.setFieldValue("otherCertifications", updated);
  };

  
  if (isLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>
          Loading medical information...
        </div>
      </div>
    );
  }

  return (
    <div className={css.medicalReport}>
        <h2>Step 3: Medical & Certifications (School Bus Driver)</h2>
        <h5>Provide details about your DOT Medical Certification, state-specific requirements, and other relevant certifications.</h5>
        <h5>Required fields are marked with *</h5>

      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Information */}
        <h3>DOT Medical Information</h3>
        <div className={css.formRow}>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label htmlFor="">DOT Medical Card Status:&nbsp;<sup>*</sup>&nbsp;<span>(Required for drivers of vehicles designed to transport 16+ passengers. See 49 CFR §391.41)</span></label>
            </div>
            <Dropdown
              options={medicalStatusOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
              value={formik.values.dotMedicalCardStatus}
              placeholder="Select Status"
              onChange={(value) => formik.setFieldValue("dotMedicalCardStatus", value)}
              error={formik.touched.dotMedicalCardStatus && formik.errors.dotMedicalCardStatus ? formik.errors.dotMedicalCardStatus : undefined}
              name="dotMedicalCardStatus"
            />
          </div>
        </div>

          {/* Conditional Fields based on Status */}
          {formik.values.dotMedicalCardStatus &&
            requiresExpirationDate(formik.values.dotMedicalCardStatus) && (
              <div className={css.formRow}>
                {/* DOT Medical Card Expiration Date */}
                <div className={css.col03}>
                  <div className={css.labelDiv}>
                    <label htmlFor="">DOT Medical Card Expiration Date:&nbsp;<sup>*</sup></label>
                  </div>
                  <DateInput
                    selected={formik.values.dotExpirationDate}
                    onChange={(date) =>
                      formik.setFieldValue("dotExpirationDate", date)
                    }
                    placeholder="Select expiration date"
                  />
                  {formik.touched.dotExpirationDate &&
                    formik.errors.dotExpirationDate && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "0.25rem",
                        }}
                      >
                        {formik.errors.dotExpirationDate}
                      </div>
                    )}
                </div>
                <div className={css.col03}>
                  <div className={css.labelDiv}>
                    <label htmlFor="">Medical Examiner&apos;s Name:&nbsp;<span>(Optional)</span></label>
                  </div>
                  <input
                    type="text"
                    name="dotExaminerName"
                    value={formik.values.dotExaminerName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter examiner name"
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      fontSize: "16px",
                    }}
                  />
                </div>

                <div className={css.col03}>
                  <div className={css.labelDiv}>
                    <label htmlFor="">Medical Examiner&apos;s Phone:&nbsp;<span>(Optional)</span></label>
                  </div>
                  <input
                    type="tel"
                    name="dotExaminerPhone"
                    value={formik.values.dotExaminerPhone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="(*************"
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      fontSize: "16px",
                    }}
                  />
                </div>
                <div className={css.col03}>
                  <div className={css.labelDiv}>
                    <label htmlFor="" className={css.nowrap}>National Registry Number (NRCME #):&nbsp;<span>(Optional)</span></label>
                  </div>
                  <input
                    type="text"
                    name="dotNationalRegistryNumber"
                    value={formik.values.dotNationalRegistryNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter NRCME number"
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      fontSize: "16px",
                    }}
                  />
                </div>

                <div className={css.col03}>
                  <div className={css.labelDiv}>
                    <label htmlFor="">Medical Card Restrictions (if any):&nbsp;<span>(Optional)</span></label>
                  </div>
                  <input
                    type="text"
                    name="dotRestriction"
                    value={formik.values.dotRestriction}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter any restrictions"
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      fontSize: "16px",
                    }}
                  />
                </div>

                {/* Medical Variance/Exemption Details */}
                {isVarianceExemption(formik.values.dotMedicalCardStatus) && (
                  <div className={css.col03}>
                    <div className={css.labelDiv}>
                      <label htmlFor="" className={css.nowrap}>Type of Medical Variance/Exemption Document Carried:&nbsp;<sup>*</sup></label>
                    </div>
                    <input
                      type="text"
                      name="dotExemption"
                      value={formik.values.dotExemption}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="e.g., FMCSA Vision Exemption, Hearing SPE Certificate, Insulin Waiver"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                    {formik.touched.dotExemption &&
                      formik.errors.dotExemption && (
                        <span className={css.error}>
                          {formik.errors.dotExemption}
                        </span>
                      )}
                  </div>
                )}
              </div>
            )}

        {/* State-Specific School Bus Driver Certifications */}
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <h5>State-Specific School Bus Driver Certifications&nbsp;<span>(Many states require specific training, physicals, or certifications beyond the standard CDL and DOT physical)</span></h5>
          <label htmlFor="" className={css.mb16}>Do you hold any state-specific School Bus Driver Certifications / Permits?&nbsp;<sup>*</sup></label>
          <div className={`${css.radioList} ${css.mb16}`}>
            <div className={css.radioGroup}>
              <input
                type="radio"
                name="hasMedicalVariance"
                value="true"
                checked={formik.values.hasMedicalVariance === true}
                onChange={() =>
                  formik.setFieldValue("hasMedicalVariance", true)
                }
              />
              <span className={css.checkmark}></span>
              <p>Yes</p>
              
            </div>
            <div className={css.radioGroup}>
              <input
                type="radio"
                name="hasMedicalVariance"
                value="false"
                checked={formik.values.hasMedicalVariance === false}
                onChange={() =>
                  formik.setFieldValue("hasMedicalVariance", false)
                }
              />
              <span className={css.checkmark}></span>
              <p>No</p>
            </div>
            <div className={css.radioGroup}>
              <input
                type="radio"
                name="hasMedicalVariance"
                value="unsure"
                checked={formik.values.hasMedicalVariance === false}
                onChange={() =>
                  formik.setFieldValue("hasMedicalVariance", false)
                }
              />
              <span className={css.checkmark}></span>
              <p>Unsure / Check State Requirements</p>
            </div>
          </div>

          {/* State Certifications Section */}
          {formik.values.hasMedicalVariance === true && (
            <div className={`${css.formRow} ${css.dBlaco}`}>
              <label htmlFor="" className={css.mb16}>List any state-specific certifications required in your operating area.</label>
              <button
                type="button"
                onClick={addStateCertification}
                className={css.addPeriodBtn}
              >
                + Add State Certification
              </button>

              {formik.values.stateCertifications.map((cert, index) => (
                <div key={cert.id} className={css.card}>
                  <h3>State Certification {index + 1}</h3>
                  <button
                    type="button"
                    onClick={() => removeStateCertification(index)}
                    className={css.removeHistory}
                    >
                    <img src="/images/icons/icon-close.svg" alt="icon-close.svg"/>
                    Remove This Certification
                  </button>
                  <div className={css.formRow}>
                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                          <label htmlFor="">Issuing Body / Provider:&nbsp;<span>(Optional)</span></label>
                      </div>
                      <input
                        type="text"
                        name={`stateCertifications[${index}].busIssuingBody`}
                        value={cert.busIssuingBody}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., American Heart Assoc., Red Cross"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>
                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                          <label htmlFor="">Certification Name / Type:<sup>*</sup></label>
                      </div>
                      <input
                        type="text"
                        name={`stateCertifications[${index}].certificationName`}
                        value={cert.certificationName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., State DOE School Bus Driver Certificate"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                      {formik.touched.stateCertifications?.[index]?.certificationName &&
                      typeof formik.errors.stateCertifications?.[index] === "object" &&
                      formik.errors.stateCertifications?.[index]?.certificationName && (
                        <span className={css.error}>
                          {formik.errors.stateCertifications[index]?.certificationName}
                        </span>
                      )}
                    </div>
                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                          <label htmlFor="">Date Issued (Optional):</label>
                      </div>
                      <DateInput
                        selected={cert.dateIssued}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `stateCertifications[${index}].dateIssued`,
                            date
                          )
                        }
                        placeholder="Select issue date"
                      />
                    </div>
                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                          <label htmlFor="">Expiration Date (if applicable):</label>
                      </div>
                      <DateInput
                        selected={cert.expirationDate}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `stateCertifications[${index}].expirationDate`,
                            date
                          )
                        }
                        placeholder="Select expiration date"
                      />
                      {formik.touched.stateCertifications?.[index]?.expirationDate &&
                      typeof formik.errors.stateCertifications?.[index] === "object" &&
                      formik.errors.stateCertifications?.[index]?.expirationDate  && (
                        <span className={css.error}>
                          {formik.errors.stateCertifications[index]?.expirationDate }
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Other Relevant Certifications */}
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <h5>Other Relevant Certifications <span>(Optional - e.g., First Aid, CPR, Passenger Assistance)</span></h5>
          <label htmlFor="" className={css.mb16}>Do you hold certifications like First Aid, CPR, or Passenger Assistance Training (PATS)?</label>
          <div className={`${css.radioList} ${css.mb16}`}>
              <div className={css.radioGroup}>
                <input
                  type="radio"
                  name="holdOtherCertification"
                  value="true"
                  checked={formik.values.holdOtherCertification === true}
                  onChange={() =>
                    formik.setFieldValue("holdOtherCertification", true)
                  }
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
              </div>
              <div className={css.radioGroup}>
                <input
                  type="radio"
                  name="holdOtherCertification"
                  value="false"
                  checked={formik.values.holdOtherCertification === false}
                  onChange={() =>
                    formik.setFieldValue("holdOtherCertification", false)
                  }
                />
                <span className={css.checkmark}></span>
                <p>No</p>
              </div>
          </div>

          {/* Other Certifications Section */}
          {formik.values.holdOtherCertification === true && (
            <div className={`${css.formRow} ${css.dBlaco}`}>
              <label htmlFor="" className={css.mb16}>List relevant certifications below.</label>
              <button
                type="button"
                onClick={addOtherCertification}
                className={css.addPeriodBtn}
                >
                + Add Certification
              </button>

              {formik.values.otherCertifications.map((cert, index) => (
                <div className={css.card} key={index}>
                  <h3>Certification {index + 1}</h3>
                  <button
                    type="button"
                    onClick={() => removeOtherCertification(index)}
                    className={css.removeHistory}
                  >
                    <img src="/images/icons/icon-close.svg" alt="icon-close.svg"/>
                    Remove This Certification
                  </button>
                  <div className={css.formRow}>
                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                        <label>Certification Name / Type:<sup>*</sup></label>
                      </div>
                      <input
                        type="text"
                        name={`otherCertifications[${index}].certificationName`}
                        value={cert.certificationName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., CPR/AED Certification, Basic First Aid"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                      {formik.touched.stateCertifications?.[index]?.certificationName &&
                        typeof formik.errors.stateCertifications?.[index] === "object" &&
                        formik.errors.stateCertifications?.[index]?.certificationName && (
                          <span className={css.error}>
                            {formik.errors.stateCertifications[index]?.certificationName}
                          </span>
                      )}
                    </div>

                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                        <label>Issuing Body / Provider: (Optional)</label>
                      </div>
                      <input
                        type="text"
                        name={`otherCertifications[${index}].issuingBody`}
                        value={cert.issuingBody}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., American Heart Assoc., Red Cross"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>

                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                        <label>Date Issued (Optional):</label>
                      </div>
                      <DateInput
                        selected={cert.dateIssued}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `otherCertifications[${index}].dateIssued`,
                            date
                          )
                        }
                        placeholder="Select issue date"
                      />
                    </div>

                    <div className={css.col02}>
                      <div className={css.labelDiv}>
                        <label>Expiration Date (if applicable):</label>
                      </div>
                      <DateInput
                        selected={cert.expirationDate}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `otherCertifications[${index}].expirationDate`,
                            date
                          )
                        }
                        placeholder="Select expiration date"
                      />
                      {formik.touched.stateCertifications?.[index]?.expirationDate &&
                      typeof formik.errors.stateCertifications?.[index] === "object" &&
                      formik.errors.stateCertifications?.[index]?.expirationDate && (
                        <span className={css.error}>
                          {formik.errors.stateCertifications[index]?.expirationDate}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              className={css.back}
              >
                <img src="/images/icons/arrow_back.svg"/>
                Back
            </button>
          )}

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              disabled={isLoading}
              className={css.exit}
            >
              Save & Exit
            </button>

            <button
              type="submit"
              disabled={isLoading}
              className={css.continue}
              >
              {isLoading ? "Saving..." : "Save & Continue (To Step 4: Docs)"}
            </button>
        </div>
      </form>
    </div>
  );
};

export default Medical;
