export const numRegex = /^[0-9\b]+$/;
export const numDecimalRegex = /^\d*\.?\d*$/;

export const jobTitleDropdown = [
  { label: "Local Driver", value: 1 },
  { label: "Regional Driver", value: 2 },
  { label: "OTR Driver", value: 3 },
  { label: "Dedicated Route", value: 4 },
  { label: "Other", value: 5 },
];

export const employmentTypes = [
  "Full-time",
  "Part-time",
  "Contract",
  "Lease Purchase Option Available",
  "Full-time",
  "Part-time",
  "Contract",
  "Lease Purchase Option Available",
];

export const stateDropdown = [
  { label: "Alabama", value: 1 },
  { label: "Wyoming", value: 2 },
];

export const routeTypes = [
  "OTR (Over-the-Road)",
  "Regional",
  "Other (Describe Below)",
];

export const areaTypes = [
  { label: "Northeast US", value: "Northeast US" },
  { label: "Southeast US", value: "Southeast US" },
  {
    label: "Specific States (List Below)",
    value: "Specific States (List Below)",
  },
];

export const frequencyTypes = [
  "Home Daily",
  "Home Weekends (Typically 34-48 hours)",
  "Home Weekly (May be during the week)",
  "Custom (Describe Below)",
];

export const workScheduleTypes = [
  "Monday-Friday",
  "Includes Weekend Work",
  "Flexible Schedule",
];

export const joiningTypes = [
  "Immediate Opening",
  "Within 1 week",
  "Future Opening / Training Class",
];

export const payTypes = [
  { label: "Per Mile", value: "PER_MILE" },
  { label: "Hourly", value: "HOURLY" },
  { label: "Per Day", value: "PER_DAY" },
  { label: "Per Week", value: "PER_WEEK" },
  { label: "Percentage of Load", value: "PERCENTAGE_OF_LOAD" },
  { label: "Per Load / Stop", value: "PER_LOAD" },
  { label: "Salary", value: "SALARY" },
  { label: "Combination / Other", value: "COMBINATION_OTHER" },
];

export const paySchoolTypes = [
  { label: "Hourly", value: "HOURLY" },
  { label: "Per Day", value: "PER_DAY" },
  { label: "Per Route", value: "PER_ROUTE" },
  { label: "Salary", value: "SALARY" },
  { label: "Other", value: "COMBINATION_OTHER" },
];

export const payBonusOffer = [
  { label: "Safety Bonus", value: "safetyBonus" },
  { label: "Performance Bonus", value: "performanceBonus" },
  { label: "Referral Bonus", value: "referralBonus" },
  { label: "Attendance Bonus", value: "attendanceBonus" },
  { label: "Detention Pay", value: "detentionPay" },
  { label: "Layover Pay", value: "layoverPay" },
  { label: "Stop Pay", value: "stopPay" },
  { label: "Tarping Pay (Flatbed)", value: "tarpingPay" },
  { label: "Loading / Unloading Pay", value: "loadingUnloadingPay" },
  { label: "NYC Pay Differential", value: "nycPayDifferential" },
  { label: "Canada / Mexico Border Crossing Pay", value: "borderCrossingPay" },
  { label: "Per Diem (Tax Advantage Plan)", value: "perDiem" },
  { label: "Holiday Pay", value: "holidayPay" },
  { label: "Other", value: "other" },
];

export const bonusOfferArray = [
  "safetyBonus",
  "performanceBonus",
  "referralBonus",
  "attendanceBonus",
  "detentionPay",
  "stopPay",
  "tarpingPay",
  "loadingUnloadingPay",
  "nycPayDifferential",
  "borderCrossingPay",
  "perDiem",
  "holidayPay"
];

export const schoolBonusOfferArray = [
  "attendanceBonus",
  "safetyBonus",
  "referralBonus",
  "fieldPay",
  "routePay",
  "cdlTraining",
  "longevityPay"
];

export const booleanFlags = [
  { label: "Yes", value: true },
  { label: "No", value: false },
];

export const pickUpDistance = [
  { label: "1 - 3 Miles", value: 1 },
  { label: "4 - 6 Miles", value: 2 },
  { label: "7 - 9 Miles", value: 3 },
  { label: "10+ Miles", value: 4 },
];

export const guaranteedPayDetails = ["Day", "Week", "Pay Period"];

export const estimateDetails = ["Per Week", "Per Year"];

export const payScheduleDetails = ["Weekly", "Bi-weekly", "Other"];

export const expensesDetails = ["Travel", "Lodging", "Meals"];

export const postingVisibility = [
  {
    label:
      "All published job postings are Public. They are partially visible to the public and fully visible to logged-in Drivers who have completed Stage 2 registration.",
    value: "Public",
  },
];

export const checkboxState = [
  {
    label: `I confirm that this job posting complies with all applicable equal employment opportunity (EEO), state, and federal regulations for school transportation personnel.`,
    value: 1,
  },
];

export const ageRequirement = [
  { label: "18 years old", value: 18 },
  { label: "21 years old", value: 21 },
];

export const percentageData = [
  { label: "Line Haul Revenue", value: "Line Haul Revenue" },
  { label: "Gross Revenue", value: "Gross Revenue" },
  { label: "Other", value: "Other" }
]

export const otherValue = { label: "Other", value: "other" };

export const driverRecords = [
  {
    title: "Moving Violations",
    key: "numberOfMovingViolations",
    radioFieldName: "numberOfMovingViolations.allowed",
    dropdownField: "numberOfMovingViolations.count",
    dropdownField1: "numberOfMovingViolations.years",
    placeholder: "E.g; Max 1",
    placeholder1: "E.g; 5 years"
  },
  {
    title: "Major Accidents",
    key: "numberOfAccidents",
    radioFieldName: "numberOfAccidents.allowed",
    dropdownField: "numberOfAccidents.count",
    dropdownField1: "numberOfAccidents.years",
    placeholder: "E.g; None",
    placeholder1: "E.g; 5 years"
  },
  {
    title: "Preventable Accidents",
    key: "preventableAccidentsYears",
    radioFieldName: "preventableAccidentsYears.allowed",
    dropdownField: "preventableAccidentsYears.count",
    dropdownField1: "preventableAccidentsYears.years",
    placeholder: "E.g; None",
    placeholder1: "E.g; 5 years"
  },
  {
    title: "Serious Violations",
    key: "seriousViolationsYears",
    radioFieldName: "seriousViolationsYears.allowed",
    dropdownField: "seriousViolationsYears.count",
    dropdownField1: "seriousViolationsYears.years",
    placeholder: "E.g; None",
    placeholder1: "E.g; 5 years"
  },
  {
    title: "DUI/DWI Convictions",
    key: "duiYears",
    radioFieldName: "duiYears.allowed",
    dropdownField: "duiYears.count",
    dropdownField1: "duiYears.years",
    placeholder: "E.g; None",
    placeholder1: "E.g; 5 years"
  },
  {
    title: "License Suspension or Revoke",
    key: "suspensionYears",
    radioFieldName: "suspensionYears.allowed",
    dropdownField: "suspensionYears.count",
    dropdownField1: "suspensionYears.years",
    placeholder: "E.g; None",
    placeholder1: "E.g; 5 years"
  }
]

export const driverSelection = [
  { label: "None", value: 0 },
  { label: 1, value: 1 },
  { label: 2, value: 2 },
  { label: 3, value: 3 },
  { label: 4, value: 4 },
  { label: 5, value: 5 },
  { label: 6, value: 6 },
  { label: 7, value: 7 },
  { label: 8, value: 8 },
  { label: 9, value: 9 },
  { label: 10, value: 10 }
]

export const proficiencyOptions = [
  { label: "Elementary", value: "ELEMENTARY" },
  { label: "Limited Working", value: "LIMITED_WORKING" },
  { label: "Professional Working", value: "PROFESSIONAL_WORKING" },
  { label: "Full Professional", value: "FULL_PROFESSIONAL" },
  { label: "Native / Bilingual", value: "NATIVE_BILINGUAL" }
];

export const experienceStudentsReq = [
  { label: "Required", value: "Required" },
  { label: "Preferred", value: "Preferred" },
  { label: "Not Required", value: "Not Required" }
]
