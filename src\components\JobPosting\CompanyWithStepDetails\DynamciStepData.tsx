import { dynamicComponentMap, stepHeading } from "./DynamicComponentMap";
import { JobPostingProps } from "../../../types/jobpostingform";
import { Dispatch, SetStateAction } from "react";

type StepParams = JobPostingProps & {
  setCurrentStep: Dispatch<SetStateAction<number>>;
  jobCategoryId: string | number;
  setLoading: (loading: boolean) => void;
};

interface StepData {
  title: string;
  subCategory: string;
}

export const getSteps = ({
  jobCategoryId,
  lang,
  formFields,
  setCurrentStep,
  companyDetails,
  states,
  languages,
  otherLanguages,
  setLoading
}: StepParams) => {
  const components = dynamicComponentMap[jobCategoryId];
  const jobPostingSteps = stepHeading[jobCategoryId];

  if (!components || !jobPostingSteps) {
    return [
      {
        title: "Unsupported Job Category",
        subCategory: "",
        component: <div>Job type not supported. Please contact support.</div>,
      },
    ];
  }

  const steps = [
    "BasicForm",
    "PayStructure",
    "TruckDetails",
    "Qualifications",
    "JobDescription",
  ] as const;

  return jobPostingSteps.map((list: StepData, index: number) => {
    const Component = components[steps[index]];

    return {
      title: list.title,
      subCategory: list.subCategory,
      component: (
        <Component
          lang={lang}
          formFields={formFields?.[index + 1]}
          setCurrentStep={setCurrentStep}
          companyDetails={companyDetails}
          states={states}
          languages={languages}
          otherLanguages={otherLanguages}
          setLoading={setLoading}
        />
      ),
    };
  });
};
