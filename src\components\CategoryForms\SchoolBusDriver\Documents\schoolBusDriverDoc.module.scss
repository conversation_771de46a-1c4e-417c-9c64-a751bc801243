@import '../../../../styles/global.scss';

.driversRegistration {
    .container {
        @include container;
    }

    h5 {
        color: #555555;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 16px;
    }

    .required {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;

        span {
            color: $red;
        }
    }

    .flexBox {
        display: flex;
        flex-wrap: wrap;
        gap: 32px;
        margin-top: 32px;

        &.mt32 {
            margin-top: 32px;
        }

        .col02 {
            width: calc((100% - 32px) / 2);

            @include for-size(tablet-phone) {
                width: 100%;
            }
        }
    }

    .labelDiv {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        &.mb8 {
            margin-bottom: 8px;
        }

        button {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 21px;
            cursor: pointer;
            margin-left: auto;
        }
    }

    label {
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;


        span {
            font-weight: 400;

            @include for-size(tablet-phone) {
                font-size: 12px;
            }
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    .error {
        color: #F91313;
        font-size: 12px;
        line-height: 18px;
        font-weight: 400;
        margin-top: 4px;
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 340px;
            }
        }
    }
}
