"use client";

import React, { useEffect, useState } from "react";
import { getIn, useFormik } from "formik";
import { ageRequirement, booleanFlags, driverRecords, driverSelection, otherValue, proficiencyOptions } from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import { QualificationForm, QualifyProps } from "../../../../types/jobpostingform";
import DropdownField from "@/components/Common/Form/DropdownField";
import Textarea from "@/components/Common/Form/Textarea";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { addAllowedFields, deep<PERSON>leanValues, getCommon<PERSON>eys, handleArrayFields, languageReq, safeFetch, scrollToFirstError } from "@/utils/utils";
import { qualifyBasicValues } from "@/initialValues/qualificationFormValues";
import { getQualifyFormSchema } from "@/schemas/qaulificationSchema";
import { qualifyFormPayload } from "@/submitHandler/qualificationSubmit";

const Qualifications = ({ formFields, setCurrentStep, languages, otherLanguages, setLoading }: QualifyProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const combinedLanguages = [...languages, ...otherLanguages];
  const proficiencyValues = proficiencyOptions.map((list) => list.value);
  const languageValues = Array.from(
    new Set(
      combinedLanguages
        .map((lang) => lang?.value)
        .filter((list): list is string | number => typeof list === "string" || typeof list === "number")
    )
  );

  const formik = useFormik<QualificationForm>({
    initialValues: qualifyBasicValues,
    validationSchema: getQualifyFormSchema(isDraft, proficiencyValues, languageValues),
    onSubmit: async (values: QualificationForm) => {
      setLoading(true);
      const payload = qualifyFormPayload(values, isDraft);

      await safeFetch(
        () => jobPostingUpdate(payload, jobId, setCurrentStep, 5, isDraft, router),
        {},
        () => setLoading(false)
      );
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});

      if(result?.jobPost) {
        const initialKeys = getCommonKeys(qualifyBasicValues, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);
        
        const arrayFields = handleArrayFields(cleanedJobPost, {
          physicalRequirements: { outputKey: "otherPhysicalRequirements", field1DependsOn: "physicalLiftingLimit", addField: "other", addField1: "radioBtn" },
          otherRequirements: { outputKey: "otherRequirementsText", addField: "other" },
          endorsements: { },
          preferredEquipment: { },
          preferredRoutes: { },
          screeningChecks: { }
        });

        const drivingRecordReq = addAllowedFields(cleanedJobPost);

        const filtereddriverLanguages = cleanedJobPost?.driverLanguages?.map(({ languageId, proficiency }) => ({
          languageId: String(languageId),
          proficiency,
        }));

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...drivingRecordReq,
          driverLanguages: filtereddriverLanguages
        });
      }
    };

    fetchData();
  }, [])

  return (
    <div className={styles.payStructureInfo}>
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>License & Endorsements</h2>
          <RadioField
            label="Required CDL Class"
            fieldName="cdlClass"
            formik={formik}
            radioArray={formFields?.["cdl-requirement-job-posting-cdl"]}
          />
          <RadioField
            label="Valid DOT Medical Card Required?"
            fieldName="dotMedicalCard"
            formik={formik}
            radioArray={booleanFlags}
          />
          <CheckboxField
            label="Required Endorsements"
            desc=" - Check all that apply"
            fieldName="endorsements"
            formik={formik}
            checkboxArray={formFields?.["cdl-endorsements-job-posting-cdl"]}
            hide={true}
          />
          <RadioField
            label="Air Brake Certification Required?"
            desc=" - Confirm driver does not have L/Z restriction"
            fieldName="airbrakeCertRequired"
            formik={formik}
            radioArray={booleanFlags}
          />
          <RadioField
            label="TWIC Card Required?"
            fieldName="twicCardRequired"
            formik={formik}
            radioArray={booleanFlags}
          />
          <RadioField
            label="Passport / FAST Card / Enhanced License Required?"
            desc=" (For border crossing)"
            fieldName="passportRequired"
            formik={formik}
            radioArray={booleanFlags}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Experience Requirements</h2>
          <RadioField
            label="Minimum Verifiable CDL Experience"
            desc=" (Class specified above)"
            fieldName="experienceMonths"
            formik={formik}
            radioArray={formFields?.["experience-level-job-posting-cdl"]}
          />
          <CheckboxField
            label="Specific Equipment Experience Preferred / Required"
            desc=" - Check all that apply"
            fieldName="preferredEquipment"
            formik={formik}
            checkboxArray={
              formFields?.["equipment-experience-type-job-posting-cdl"]
            }
            hide={true}
          />
          <CheckboxField
            label="Specific Route Type Experience Preferred"
            desc=" - Check all that apply"
            fieldName="preferredRoutes"
            formik={formik}
            checkboxArray={
              formFields?.["driving-routes-and-experience-job-posting-cdl"]
            }
            hide={true}
          />
          <RadioField
            label="Willing to Train Recent CDL Graduates?"
            desc=" (Any Type)"
            fieldName="willingToTrain"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.willingToTrain && 
            <Textarea
              label="Describe Training Program"
              fieldName="trainingProgram"
              placeholder="e.g., 6-week paid training with certified trainer."
              formik={formik}
              hide={true}
            />
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Driving Record Requirements</h2>
          <label className={styles.driverReq}>Specify Driving Record Requirements<span> - Be specific and check all that apply</span><span className={styles.mandatory}>*</span></label>
          {driverRecords.map((list) => {
            const isRadioSelected = getIn(formik.values, list.radioFieldName) === true;

            return (
              <div className={styles.wrapperContainer} key={list.title}>
                <span className={styles.titleClass}>{list.title}</span>
                <RadioField
                  styleClass={styles.radioFormWidth}
                  fieldName={list.radioFieldName}
                  formik={formik}
                  radioArray={booleanFlags}
                  hide={true}
                />
                <div className={styles.dropdownClass}>
                  <DropdownField 
                    className="dropdownWidth"
                    fieldName={list.dropdownField}
                    defaultLabel={list.placeholder}
                    formik={formik}
                    dropdownArray={list.dropdownField === "numberOfMovingViolations.count" 
                      ? driverSelection.slice(1, 11) : driverSelection
                    }
                    disabled={!isRadioSelected}
                  />
                  <span className={styles.textSpan}>In</span>
                  <DropdownField 
                    className="dropdownWidth"
                    fieldName={list.dropdownField1}
                    defaultLabel={list.placeholder1}
                    formik={formik}
                    dropdownArray={driverSelection.slice(1, 11)}
                    disabled={!isRadioSelected}
                  />
                </div>
              </div>
            )
          })}
          <div className={styles.wrapperContainer}>
            <span className={styles.titleClass}>Other</span>
            <TextField
              className="columnWidth_4"
              fieldName="drivingRecordOther"
              placeholder="Enter here"
              formik={formik}
              hide={true}
            />
          </div>
          {formik.touched.drivingReq && formik.errors.drivingReq && typeof formik.errors.drivingReq === "string" && (
            <div className="error_msg">
              {formik.errors.drivingReq}
            </div>
          )}
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>
            Background Check & Screening Requirements
          </h2>
          <CheckboxField
            className="fullWidthAdjust"
            label="Screening Required"
            desc=" - Check all that apply - Many are legally mandated"
            fieldName="screeningChecks"
            formik={formik}
            checkboxArray={
              formFields?.["pre-employment-screening-testing-job-posting-cdl"]
            }
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Physical Requirements</h2>
          <CheckboxField
            className="fullWidthAdjust"
            label="Specify Physical Requirements"
            desc=" - Check all that apply"
            fieldName="physicalRequirements"
            formik={formik}
            checkboxArray={[ 
              ...(formFields?.["physical-abilities-requirements-job-posting-cdl"] || []),
              { label: "Ability to lift / carry up to  - (Specify based on loading req.)",
                value: "radioBtn"
              },
              otherValue
            ]}
            radioBtn={true}
            radioClassName="checkboxRadioAlign"
            radioFieldName="physicalLiftingLimit"
            radioArray={formFields?.["ability-to-lift-carry-up-to-job-posting-cdl"]}
          />
          {formik.values.physicalRequirements.includes("other") && 
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="otherPhysicalRequirements"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          {formik.touched.physicalReq && typeof formik.errors.physicalReq === "string" && (
            <div className="error_msg">{formik.errors.physicalReq}</div>
          )}
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Language Requirements</h2>
          {languageReq(languages, otherLanguages).map((list, idx) => 
            <div key={list.key} className={styles.rowField}>
              {list.fields.map((item) => 
                <DropdownField 
                  key={item.field}
                  className="columnWidth_3"
                  label={item.title}
                  fieldName={`driverLanguages.${idx}.${item.field}`}
                  defaultLabel={item.defaultLabel}
                  formik={formik}
                  dropdownArray={item.options}
                  hide={true}
                />
              )}
            </div>
          )}
          {formik.touched.driverLanguages && typeof formik.errors.driverLanguages === "string" && (
            <div className="error_msg">{formik.errors.driverLanguages}</div>
          )}
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Additional Requirements</h2>
          <RadioField
            label="What is the minimum age requirement to apply?"
            fieldName="minAge"
            formik={formik}
            radioArray={ageRequirement}
          />
          <RadioField
            label="Other Requirements"
            desc=" - Check all that apply"
            fieldName="isOtherRequirements"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.isOtherRequirements &&
            <>
              <CheckboxField
                className="fullWidthAdjust"
                fieldName="otherRequirements"
                formik={formik}
                checkboxArray={[ 
                  ...(formFields?.["other-requirements-job-posting-cdl"] || []),
                  otherValue
                ]}
                hide={true}
              />
              {formik.values.otherRequirements.includes("other") &&
                <div className={styles.rowField}>
                  <TextField
                    className="columnWidth_3"
                    fieldName="otherRequirementsText"
                    placeholder="Enter here"
                    formik={formik}
                    hide={true}
                  />
                </div>
              }
            </>
          }
          <ButtonComponent
            backBtnHandler={() => setCurrentStep(3)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm()
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default Qualifications;
