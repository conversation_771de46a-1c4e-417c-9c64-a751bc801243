import { DescriptionFormValues, DescSchoolValues } from "@/types/jobpostingform";

export const jobFormPayload = (
  values: DescriptionFormValues,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(
    JSON.stringify(values)
  ) as DescriptionFormValues;

  const jobPost: Record<string, unknown> = {
    jobDescription: cloneObject.jobDescription,
    keyResponsibilities: cloneObject.keyResponsibilities,
    applicationMethod: cloneObject.applicationMethod,
    applicationDocs: typeof cloneObject?.applicationDocs === 'boolean' ? cloneObject?.applicationDocs : null,
    interviewSteps: cloneObject.interviewSteps,
    hiringProcessTimeline: cloneObject.hiringProcessTimeline
      ? Number(cloneObject.hiringProcessTimeline)
      : null,
    contactPersonName: cloneObject.contactPersonName,
    contactPersonEmail: cloneObject.contactPersonEmail,
    contactPersonPhone: cloneObject.contactPersonPhone,
    visibility: "Public",
    postingDurationDays: 30,
    eeoConfirmed: cloneObject.eeoConfirmed.includes(1) ? true : false,
    additionalNotes: (cloneObject?.applicationDocs && cloneObject.additionalNotes) ? cloneObject.additionalNotes : ""
  };

  jobPost.joiningDate = cloneObject.joiningDate
    ? (
      cloneObject.joiningDate instanceof Date
        ? cloneObject.joiningDate.toISOString()
        : cloneObject.joiningDate
      )
    : null;

  return {
    currentStep: 5,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
      
    },
  };
};

export const jobSchoolFormPayload = (
  values: DescSchoolValues,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(
    JSON.stringify(values)
  ) as DescSchoolValues;

  const jobPost: Record<string, unknown> = {
    jobDescription: cloneObject.jobDescription,
    keyResponsibilities: cloneObject.keyResponsibilities,
    applicationMethod: cloneObject.applicationMethod,
    applicationDocs: typeof cloneObject?.applicationDocs === 'boolean' ? cloneObject?.applicationDocs : null,
    interviewSteps: cloneObject.interviewSteps,
    hiringProcessTimeline: cloneObject.hiringProcessTimeline
      ? Number(cloneObject.hiringProcessTimeline)
      : null,
    contactPersonName: cloneObject.contactPersonName,
    contactPersonEmail: cloneObject.contactPersonEmail,
    contactPersonPhone: cloneObject.contactPersonPhone,
    visibility: "Public",
    postingDurationDays: 30,
    eeoConfirmed: cloneObject.eeoConfirmed.includes(1) ? true : false,
    workingConditions: cloneObject.workingConditions,
    routeEnvironments: cloneObject.routeEnvironments,
    routeType: cloneObject.routeType,
    firstPickUp: cloneObject.firstPickUp,
    lastDropOffLocation: cloneObject.lastDropOffLocation,
    totalRouteMileage: cloneObject.totalRouteMileage,
    additionalNotes: (cloneObject?.applicationDocs && cloneObject.additionalNotes) ? cloneObject.additionalNotes : ""
  };

  jobPost.joiningDate = cloneObject.joiningDate
    ? (
      cloneObject.joiningDate instanceof Date
        ? cloneObject.joiningDate.toISOString()
        : cloneObject.joiningDate
      )
    : null;

  return {
    currentStep: 5,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
      
    },
  };
};
