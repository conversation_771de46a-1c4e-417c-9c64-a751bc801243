import { PayloadData, PayStructureForm, TransformedFields } from "@/types/jobpostingform";
import { bonusOfferArray } from "@/utils/constant";
import { normalizePerString } from "@/utils/utils";

export const payFormPayload = (values: PayStructureForm, isDraft: boolean, formFields: TransformedFields) => {
  const payloadData: PayloadData = {};
  const cloneObject = JSON.parse(JSON.stringify(values)) as PayStructureForm;

  if (cloneObject.type === "PER_MILE") {
    if (cloneObject.startingCpm)
      payloadData.startingCpm = Number(cloneObject.startingCpm);
    if (cloneObject.mileageCalculationMethod)
      payloadData.mileageCalculationMethod =
        cloneObject.mileageCalculationMethod;
    if (cloneObject.cpmRangeMin && cloneObject.cpmRangeMax) {
      payloadData.cpmRangeMin = Number(cloneObject.cpmRangeMin);
      payloadData.cpmRangeMax = Number(cloneObject.cpmRangeMax);
    }
  }

  if (cloneObject.type === "HOURLY") {
    if (cloneObject.startingHourlyRate)
      payloadData.startingHourlyRate = Number(cloneObject.startingHourlyRate);
    if (cloneObject.overtimeOption && cloneObject.overtimeOption !== "other") {
      payloadData.overtimeOption = cloneObject.overtimeOption;
    }
    if (
      cloneObject.overtimeOption === "other" &&
      cloneObject.overtimeAfterHours
    ) {
      payloadData.overtimeAfterHours = Number(cloneObject.overtimeAfterHours);
    }

    if (cloneObject.hourlyRateRangeMin && cloneObject.hourlyRateRangeMax) {
      payloadData.hourlyRateRangeMin = Number(cloneObject.hourlyRateRangeMin);
      payloadData.hourlyRateRangeMax = Number(cloneObject.hourlyRateRangeMax);
    }
  }

  if (cloneObject.type === "PER_DAY") {
    if (cloneObject.payPerDay)
      payloadData.payPerDay = Number(cloneObject.payPerDay);
    if (cloneObject.typicalHoursPerDay) {
      payloadData.typicalHoursPerDay = cloneObject.typicalHoursPerDay;
    }
  }

  if (cloneObject.type === "PER_WEEK") {
    if (cloneObject.payPerWeek)
      payloadData.payPerWeek = Number(cloneObject.payPerWeek);
    if (cloneObject.typicalHoursDaysWeek) {
      payloadData.typicalHoursDaysWeek = cloneObject.typicalHoursDaysWeek;
    }
  }

  if (cloneObject.type === "PERCENTAGE_OF_LOAD") {
    if (cloneObject.percentageRate)
      payloadData.percentageRate = Number(cloneObject.percentageRate);
    if (cloneObject.percentageBasedOn)
      payloadData.percentageBasedOn = cloneObject.percentageBasedOn;
  }

  if (cloneObject.type === "PER_LOAD") {
    if (cloneObject.payPerLoad)
      payloadData.payPerLoad = Number(cloneObject.payPerLoad);
    if (cloneObject.additionalPayFactors)
      payloadData.additionalPayFactors = cloneObject.additionalPayFactors;
  }

  if (cloneObject.type === "SALARY") {
    if (cloneObject.annualSalary)
      payloadData.annualSalary = Number(cloneObject.annualSalary);
    if (cloneObject.salaryRangeMin && cloneObject.salaryRangeMax) {
      payloadData.salaryRangeMin = Number(cloneObject.salaryRangeMin);
      payloadData.salaryRangeMax = Number(cloneObject.salaryRangeMax);
    }
  }

  if (cloneObject.type === "COMBINATION_OTHER") {
    payloadData.combinationDescription = cloneObject.combinationDescription;
  }

  if (
    cloneObject.averageEarningsEstimate &&
    cloneObject.averageEarningsEstimate1 &&
    cloneObject.estimatePeriod
  ) {
    const labelVal =
      formFields?.["period-frequency-job-posting-cdl"].find(
        (list) => list.value === cloneObject.estimatePeriod
      )?.label ?? "";
    const period = normalizePerString(labelVal);
    payloadData.averageEarningsEstimate = `${cloneObject.averageEarningsEstimate}-${cloneObject.averageEarningsEstimate1}/${period}`;
  }

  if (cloneObject.signOnBonusOffered) {
    if (cloneObject.signOnBonusAmount)
      payloadData.signOnBonusAmount = Number(cloneObject.signOnBonusAmount);
    if (cloneObject.signOnBonusPayoutStructure)
      payloadData.signOnBonusPayoutStructure =
        cloneObject.signOnBonusPayoutStructure;
  }

  if (cloneObject.benefitsPackage) {
    if (cloneObject.benefits.includes("other") && cloneObject.otherBenefit) {
      cloneObject.benefits.push(cloneObject.otherBenefit);
    }
    payloadData.benefits = cloneObject.benefits
      .filter((item) => item !== "other")
      .map(String);
  }

  // returning true or false for bonuses selected
  const bonusFlags = bonusOfferArray.reduce((acc, key) => {
    acc[key] = cloneObject.bonusOffered.includes(key);
    return acc;
  }, {} as Record<string, boolean>);

  if (cloneObject.bonusOffered.includes("detentionPay")) {
    if (cloneObject.detentionPayRate)
      payloadData.detentionPayRate = Number(cloneObject.detentionPayRate);
    if (cloneObject.detentionAfterHours)
      payloadData.detentionAfterHours = Number(cloneObject.detentionAfterHours);
  }

  if (cloneObject.bonusOffered.includes("layoverPay")) {
    if (cloneObject.layoverPay)
      payloadData.layoverPay = Number(cloneObject.layoverPay);
  }

  if (cloneObject.bonusOffered.includes("stopPay")) {
    if (cloneObject.stopPayPerStop)
      payloadData.stopPayPerStop = Number(cloneObject.stopPayPerStop);
    payloadData.stopPayExcludeFirstLast = cloneObject
      ?.stopPayExcludeFirstLast?.[0]
      ? true
      : false;
  }

  if (cloneObject.orientationRequired) {
    if (cloneObject.orientationDuration)
      payloadData.orientationDuration = Number(cloneObject.orientationDuration);
    if (cloneObject.orientationDurationUnit)
      payloadData.orientationDurationUnit = Number(
        cloneObject.orientationDurationUnit
      );
    if (cloneObject.orientationLocation)
      payloadData.orientationLocation = cloneObject.orientationLocation;
    if (cloneObject.orientationExpense)
      payloadData.orientationExpense = cloneObject.orientationExpense;
    if (cloneObject.paidOrientation) {
      if (cloneObject.orientationPayRate)
        payloadData.orientationPayRate = Number(cloneObject.orientationPayRate);
      if (cloneObject.orientationPayUnit)
        payloadData.orientationPayUnit = Number(cloneObject.orientationPayUnit);
    }
  }

  if (cloneObject.minimumGuaranteedPay) {
    payloadData.minimumGuaranteedPay = Number(
      cloneObject.minimumGuaranteedPay
    );
  }

  if (cloneObject.minimumGuaranteedPayPeriod) {
    payloadData.minimumGuaranteedPayPeriod = Number(
      cloneObject.minimumGuaranteedPayPeriod
    );
  }

  if (cloneObject.paySchedule) {
    payloadData.paySchedule = Number(cloneObject.paySchedule);
  }

  const payStructure: Record<string, unknown> = {
    type: cloneObject.type,
    signOnBonusOffered: typeof cloneObject?.signOnBonusOffered === 'boolean' ? cloneObject?.signOnBonusOffered : null,
    benefitsPackage: typeof cloneObject?.benefitsPackage === 'boolean' ? cloneObject?.benefitsPackage : null,
    orientationRequired: typeof cloneObject?.orientationRequired === 'boolean' ? cloneObject?.orientationRequired : null,
    paidOrientation: typeof cloneObject?.paidOrientation === 'boolean' ? cloneObject?.paidOrientation : null,
    driverPerksAndPrograms: cloneObject?.driverPerksAndPrograms?.map(String),
    otherBonusDescription: (cloneObject.bonusOffered.includes("other") && cloneObject.otherBonusDescription) ?
      cloneObject.otherBonusDescription : "",
    ...payloadData,
    ...bonusFlags,
  }

  return {
    currentStep: 2,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
      payStructure: payStructure
    }
  };
};
